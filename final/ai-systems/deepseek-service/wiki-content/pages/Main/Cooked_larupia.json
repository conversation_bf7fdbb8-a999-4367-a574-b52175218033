{"title": "Cooked larupia", "namespace": "Main", "content": "{{Infobox Item\n|name = Cooked larupia\n|image = [[File:Cooked larupia.png]]\n|release = [[20 March]] [[2024]]\n|update = Varlamore: Part One\n|members = Yes\n|quest = No\n|tradeable = Yes\n|placeholder = Yes\n|equipable = No\n|stackable = No\n|noteable = Yes\n|edible = Yes\n|options = Eat, Drop\n|examine = Mmm, this looks tasty.\n|value = 4\n|weight = 0.283\n|exchange = Yes\n|id = 29146\n}}\n[[File:Cooked larupia detail.png|130px|left]]\n'''Cooked larupia''' is a type of [[hunter meat]] that restores a total of 11 [[Hitpoints]] when eaten, 6 are restored immediately, 5 are restored after a delay of 7 [[RuneScape clock|ticks]] (4.2s). Eating another hunter meat too quickly will waste any pending healing, as the most recent delayed heal will replace older effects that have not completed yet. Standard foods and potions do not interrupt this pending heal.\n\nIt can be obtained by cooking [[raw larupia]] on a [[fire]] or [[cooking range]], requiring level 31 [[Cooking]] and granting 92 [[experience]] when successful. When cooking there is a chance to fail, resulting in a  [[burnt large beast]]. The [[burn rate]] decreases as the player's Cooking level increases, and it will stop burning entirely at Cooking level 59 on both ranges and fires.\n\n==Creation==\n{{Recipe\n|skill1 = Cooking\n|skill1lvl = 31\n|skill1boostable = Yes\n|skill1exp = 92\n|members = Yes\n|ticks = 4\n|facilities = Cooking range\n|mat1 = Raw larupia\n|output1 = Cooked larupia\n}}\n\n===Cooking chance===\n{{Skilling success chart|label=Cooked larupia cooking chance|showbefore=no\n|label1=Fire or Range|low1=65|high1=390|req1=31|color1=gray|image1=Cooked larupia.png\n|label2=Hosidius +5%|low2=77|high2=402|req2=31|color2=red|image2=Cooking range icon.png\n|label3=Hosidius +10%|low3=90|high3=415|req3=79|color3=blue|image3=Cooking range icon.png\n}}\n\n{{Meat}}\n[[Category:Food]]\n[[Category:Cooking]]\n[[Category:Meat]]", "downloadedAt": "2025-09-10T00:50:33.000Z", "source": "osrs_wiki_market_intelligence_batch", "rawWikitext": true, "contentLength": 1863}