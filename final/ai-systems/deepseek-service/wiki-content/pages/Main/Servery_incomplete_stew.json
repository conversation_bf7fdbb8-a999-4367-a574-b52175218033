{"title": "Servery incomplete stew", "namespace": "Main", "content": "{{Infobox Item\n|version1 = Potato\n|version2 = Meat\n|name = Servery incomplete stew\n|image = [[File:Servery incomplete stew.png]]\n|release = [[7 January]] [[2016]]\n|update = Zeah: Great Kourend\n|members = Yes\n|quest = No\n|tradeable = No\n|placeholder = Yes\n|equipable = No\n|stackable = No\n|noteable = No\n|options = Drop\n|examine1 = I need to add some meat too.\n|examine2 = I need to add some potato too.\n|value = 1\n|weight1 = 1.000\n|weight2 = 1.200\n|id1 = 13415\n|id2 = 13416\n}}\n[[File:Servery incomplete stew detail.png|left|150x150px]]\n'''Servery incomplete stew''' is obtained by using either a [[servery cooked meat]] or [[servery potato]] on a [[bowl of water]]. It is used in creating [[servery stew]], which is served to [[Shayzien]] soldiers in the [[Hosidius]] [[mess]].\n\n==Creation==\n<tabber>\nMeat=\n{{Recipe\n|skill1 = Cooking\n|skill1lvl = 25\n|skill1boostable = Yes\n|skill1exp = 0\n|members = Yes\n|ticks = 1\n|ticksnote = Auto-creating is 1, then 2, then 3 ticks.\n|mat1 = Servery cooked meat\n|mat1cost = No\n|mat2 = Bowl of water\n|output1 = Servery incomplete stew#Meat\n|output1pic = Servery incomplete stew\n|output1cost = No\n}}\n|-|\nPotato=\n{{Recipe\n|skill1 = Cooking\n|skill1lvl = 25\n|skill1boostable = Yes\n|skill1exp = 0\n|members = Yes\n|ticks = 1\n|ticksnote = Auto-creating is 1, then 2, then 3 ticks.\n|mat1 = Servery potato\n|mat1cost = No\n|mat2 = Bowl of water\n|output1 = Servery incomplete stew#Potato\n|output1pic = Servery incomplete stew\n|output1cost = No\n}}\n</tabber>\n\n==Products==\n{{Uses material list|Servery incomplete stew#Meat|Servery incomplete stew#Potato}}\n\n{{Servery items}}\n{{Bowls}}[[Category:Servery]]", "downloadedAt": "2025-09-10T00:49:26.000Z", "source": "osrs_wiki_market_intelligence_batch", "rawWikitext": true, "contentLength": 1622}