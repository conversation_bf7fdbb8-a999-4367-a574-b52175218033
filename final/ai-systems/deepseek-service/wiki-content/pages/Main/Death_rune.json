{"title": "Death rune", "namespace": "Main", "content": "{{External|rs|rsc=Death-Rune}}\n{{Infobox Item\n|name = Death rune\n|image = [[File:Death rune.png]]\n|release = [[24 May]] [[2001]]\n|update = New magic system online!\n|members = No\n|quest = No\n|tradeable = Yes\n|placeholder = Yes\n|equipable = No\n|stackable = Yes\n|noteable = No\n|options = Drop\n|examine = Used for medium level missile spells.\n|value = 180\n|weight = 0.000\n|respawn = 200\n|exchange = Yes\n|id = 560\n}}\n[[File:Death rune detail.png|left|120px]]\n'''Death runes''' are one of the [[runes]] used to cast spells in the [[Magic]] skill. They are required to cast medium-level missile spells, like [[Wind Blast]], [[Water Blast]], [[Earth Blast]], and [[Fire Blast]]. Members with 65 [[Runecraft]] and who have completed [[Mourning's End Part II]] can craft death runes at the [[Death Altar]] by using [[pure essence]] on it, with each essence yielding one death rune. Each death rune yields 10 Runecraft experience. Players with 99 Runecraft will always craft two death runes per essence. Crafting death runes from essence is a requirement for the [[Hard Ardougne Diary]].\n\nDeath runes can be bought from all [[Magic shop]]s with a stock of 250, except for [[Tutab's Magical Market|the Magic shop on Ape Atoll]]. Several monsters, such as [[ankou]]s, [[nechryael]], and [[dark beast]]s, commonly drop death runes. They can also be found in the [[Barrows Chest]] with at least 631{{CiteTwitter|author=Mod Kieren|url=https://twitter.com/JagexKieren/status/705428283509366785|date=3 March 2016|archiveurl=https://archive.is/sB4lx|archivedate=2 September 2017|quote=You guys wanted info on Barrows loot, here it is... Full size: [http://i.imgur.com/oZSMkAX.png] Explained the best I can!}} rewards potential.\n\nPlayers can exchange any [[cat]] that is not a [[kitten]] with the [[civilian]]s in [[West Ardougne]] for 100 death runes, or 200 if they have completed the easy [[Ardougne diary]].\n\n[[Barbarian Assault]] uses [[Death rune (Barbarian Assault)|a unique variant]] of death runes that are untradeable and have an alchemy value of 0 to prevent potential exploits. [[Nightmare Zone]] also uses [[Death rune (nz)|a variant]], also being untradeable and having an alchemy value of 0, in addition to being unusable for non-combat spells.\n\n==Runecraft info==\n{{Tabber\n|tab1=Pure essence\n|tab1content={{Recipe\n|skill1 = Runecraft\n|skill1lvl = 65\n|skill1boostable = yes\n|skill1exp = 10\n|members = yes\n|ticks = 1\n|facilities = Death Altar\n|tools = Death tiara, Death talisman\n|notes = [[Multiple runes|2 runes]] per essence after level 99.\n|mat1 = Pure essence\n|output1 = Death rune\n}}\n|tab2=Daeyalt essence\n|tab2content={{Recipe\n|skill1 = Runecraft\n|skill1lvl = 65\n|skill1boostable = yes\n|skill1exp = 15\n|members = yes\n|ticks = 1\n|facilities = Death Altar\n|tools = Death tiara, Death talisman\n|notes = [[Multiple runes|2 runes]] per essence after level 99.\n|mat1 = Daeyalt essence\n|mat1cost = no\n|output1 = Death rune\n}}\n|tab3=Guardian essence\n|tab3content={{Recipe\n|skill1 = Runecraft\n|skill1lvl = 65\n|skill1boostable = yes\n|skill1exp = 10\n|members = yes\n|ticks = 1\n|facilities = Death Altar\n|notes = [[Multiple runes|2 runes]] per essence after level 99.<br>\nAccessible only in the [[Guardians of the Rift]] minigame.\n|mat1 = Guardian essence\n|mat1cost = no\n|output1 = Death rune\n|output2 = Catalytic guardian stone\n|output2cost = no\n}}\n|tab4 = Mangled extract\n|tab4content = {{Recipe\n|skill1 = Runecraft\n|skill1lvl = 65\n|notes =Started [[Desert Treasure II - The Fallen Empire]]\n|skill1boostable = yes\n|skill1exp = 10\n|members = yes\n|ticks = 1\n|facilities = Death Altar\n|tools = Death tiara, Death talisman\n|mat1 = Pure essence\n|mat2 = Mangled extract\n|mat2cost=12000\n|output1 = Death rune\n|output1quantity = 61\n|output1quantitynote = The additional 60 runes from the extract are not affected by [[Raiments of the Eye]] or skill level.\n}}\n}}\n\n==Money making==\n{{Mmgsection|Crafting death runes through the Abyss}}\n\n{{Death Rune Spells}}\n\n==Products==\n{{Uses material list|Death rune}}\n\n==Item sources==\n{{Drop sources|Death rune}}\n\n===Shop locations===\n{{Store locations list|Death rune}}\n\n====[[Rewards Guardian (Mage Training Arena)]]====\nDeath Runes can be bought for [[pizzaz points]] awarded from participating in the rooms in the [[Mage Training Arena]].\n{| class=\"wikitable\"\n!Item\n!Telekinetic\n!Alchemist\n!Enchantment\n!Graveyard\n|-\n|{{Plink|Death rune}}\n|2\n|1\n|20\n|1\n|}\n\n===Item spawns===\n{{ItemSpawnTableHead|league=yes}}\n{{ItemSpawnLine|name=Death rune|location=[[Feldip Hills]] - west of the [[gnome glider]]|members=Yes|2500,2967|leagueRegion=Kandarin}}\n{{ItemSpawnTableBottom}}\n\n==Changes==\n{{Subject changes header}}\n{{Subject changes\n|date = 10 September 2020\n|update = A Porcine of Interest\n|poll =\n|change = The [[Grand Exchange]] buy limit was changed from 10,000 to 25,000.\n}}\n{{Subject changes\n|date = 13 June 2005\n|update = RuneCraft Update and Tweaks\n|change = *The item's value was increased from 30 to 180.\n*The item was graphically updated for a second time.\n}}\n{{Subject changes\n|date = 1 June 2005\n|change = The item was graphically updated.\n}}\n{{Subject changes\n|daterange = 1 – 28 June 2004\n|change = The item's examine was changed from \"''Used for high level missile spells.''\" to \"''Used for medium level missile spells.''\"\n}}\n{{Subject changes footer}}\n\n===Gallery (historical)===\n{|style=\"text-align: center\" cellpadding=\"15\"\n|[[File:Death rune detail (historical v1).png|center|300x150px]]\n|[[File:Death rune detail (historical, v2).png|center|300x150px]]\n|[[File:Death rune detail.png|center|300x150px]]\n|-\n|[[File:Death rune (historical, v1).png|center]]\n|[[File:Death rune (historical, v2).png|center]]\n|[[File:Death rune.png|center]]\n|-\n|29 March 2004 –<br/>1 June 2005\n|1 June 2005 –<br/>13 June 2005\n|13 June 2005 –<br/>present\n|}\n\n==References==\n{{Reflist}}\n\n{{Runes}}", "downloadedAt": "2025-09-10T00:50:21.000Z", "source": "osrs_wiki_market_intelligence_batch", "rawWikitext": true, "contentLength": 5810}