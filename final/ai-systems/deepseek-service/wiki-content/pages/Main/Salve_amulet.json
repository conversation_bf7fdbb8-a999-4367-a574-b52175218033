{"title": "Salve amulet", "namespace": "Main", "content": "{{External|rs}}\n{{Infobox Item\n|name = Salve amulet\n|image = [[File:Salve amulet.png]]\n|release = [[21 December]] [[2004]]\n|update = The Haunted Mine\n|members = Yes\n|quest = [[Haunted Mine]]\n|tradeable = No\n|placeholder = Yes\n|equipable = Yes\n|stackable = No\n|noteable = No\n|options = Wear, Drop\n|examine = Increases the wearer's strength and accuracy by 15%{{Sic|actually 1/6th}} when fighting the undead.\n|value = 1\n|weight = 0.007\n|id = 4081\n}}\n[[File:Salve amulet detail.png|left|150px]]\nThe '''salve amulet''' is an offensive amulet that is made upon completion of the [[Haunted Mine]] quest, granting a 16.67%{{CiteTwitter|author=Mod Ash|url=https://twitter.com/JagexAsh/status/1171387277655846913|date=10 September 2019|archiveurl=https://archive.is/QKT8B|archivedate=26 April 2020|quote=It's 1/6, so 16% is closer.}} increase in the wearer's [[Attack]] and [[Strength]] when fighting [[Undead (attribute)|undead monsters]].\n\nThe amulet is made by using a [[chisel]] on the [[crystal outcrop]] at the bottom level of the [[Abandoned Mine]], then using the acquired [[salve shard]] on a [[ball of wool]]; the [[String Jewellery]] spell will also work.\n\n==Combat stats==\n{{Infobox Bonuses\n|astab = 0\n|aslash = 0\n|acrush = 0\n|amagic = 0\n|arange = 0\n|dstab = +3\n|dslash = +3\n|dcrush = +3\n|dmagic = 0\n|drange = 0\n|str = 0\n|rstr = 0\n|mdmg = 0\n|prayer = +3\n|slot = neck\n|image = [[File:Salve amulet equipped.png|110px]]\n|altimage = [[File:Salve amulet equipped female.png|110px]]\n}}\n\n==Creation==\nPlayers can quickly access the [[crystal outcrop]]s by climbing over the mine cart and then crawling down the eastern cart tunnel to the [[Abandoned Mine]]; after entering, players can crawl-through the other cart tunnel adjacent to them to reach the outcrops. If the player does not have 15 [[Agility]], the mine can be accessed by simply running around to the western cart tunnels, and entering the northern one.\n{{Recipe\n|skill1 = Crafting\n|skill1lvl = 1\n|skill1exp = 0\n|members = Yes\n|ticks = 2\n|mat1 = Salve shard\n|mat1cost = no\n|mat2 = Ball of wool\n|output1 = Salve amulet\n|output1cost=no\n}}\n\n==Effects and upgrades==\n:''For a list of monsters affected by the salve amulet, [[Undead (attribute)|see here]].''\nThe salve amulet's bonuses against the undead '''do not''' stack with the [[black mask]] and [[Slayer helmet|slayer helmet's]] 16.67% attack and strength bonuses; wearing both will only apply the bonuses of the salve amulet. However, the bonuses of [[Void Knight equipment]] '''do''' stack. Additionally, the amulet's effects apply to all targets when using multi-hitting melee weapons in a [[multicombat area]], such as the [[Scythe of Vitur]] and the [[special attack]] of the [[dragon 2h sword]].\n\nThe salve amulet can also be upgraded through enchanting and imbuing it:\n* Enchanting the amulet increases the amulet's undead bonus from 16.67% to 20%. This requires using [[Tarn's diary]], acquired as part of the [[Lair of Tarn Razorlor]] miniquest.\n* Imbuing the amulet extends the undead bonus to ranged (16.67%) and magic attacks (15%). This requires using either 800,000 [[Nightmare Zone]] [[Nightmare Zone#Rewards|points]] (halved upon completing the [[Combat Achievements/Hard|hard combat achievements]]), 320 [[Soul Wars]] [[Zeal Tokens]], or by using a [[scroll of imbuing]] on the amulet.\n** Amulets imbued through the latter two methods do '''not''' count for completing the [[Ardougne Diary]] task.{{CiteTwitter|author=Mod Ash|url=https://twitter.com/JagexAsh/status/1355871771208134657|date=31 January 2021|archiveurl=https://archive.is/gkzXy|archivedate=5 February 2021|quote=No, the Soul Wars version is a different item from the NMZ item, and it doesn't do a task check.}} Furthermore, amulets imbued at one location cannot be refunded at a different location.\n\n===Comparison===\n{| class=\"wikitable\"\n! colspan=\"2\" rowspan=\"2\" |Items\n! colspan=\"2\" |Melee\n! colspan=\"2\" |Ranged\n! colspan=\"2\" |Magic\n|-\n!Accuracy\n!Damage\n!Accuracy\n!Damage\n!Accuracy\n!Damage\n|-\n|{{plinkt|Salve amulet}}\n|16.67%\n|16.67%\n|0%\n|0%\n|0%\n|0%\n|-\n|{{plinkt|Salve amulet (e)}}\n|20%\n|20%\n|0%\n|0%\n|0%\n|0%\n|-\n|{{plinkt|Salve amulet(i)}}\n|16.67%\n|16.67%\n|16.67%\n|16.67%\n|15%\n|15%\n|-\n|{{plinkt|Salve amulet(ei)}}\n|20%\n|20%\n|20%\n|20%\n|20%\n|20%\n|}\n\n==Products==\n{{Uses material list|Salve amulet}}\n\n==Used in recommended equipment==\n{{Used in recommended equipment|Salve amulet}}\n\n==Changes==\n{{Subject changes header}}\n{{Subject changes\n|date = 11 January 2023\n|update = Secrets of the North & More!\n|change = The salve amulet's combat bonuses now apply to [[Ghost (Soul Wars)|ghosts]] within [[Soul Wars]].\n}}\n{{Subject changes\n|date = 28 September 2022\n|update = Tombs of Amascut & Group Ironman Changes\n|change = Salve Amulets now have a Make-All dialogue for stringing them, as other amulets do.\n}}\n{{Subject changes\n|date = 8 August 2019\n|update = Smithing and Silver Crafting Interfaces\n|poll =\n|change = Some brown patches near the salve shard around the amulet's inventory icon have been removed.\n}}\n{{Subject changes\n|date = 1 May 2014\n|update = Spirit trees, clues & more!\n|change = The Salve amulet and its enchanted version can now be imbued at Nightmare zone for 800,000 points.\n}}\n{{Subject changes footer}}\n\n===Gallery (historical)===\n{|style=\"text-align: center\" cellpadding=\"15\"\n|[[File:Salve amulet (v1) detail.png|center|300x150px]]\n|[[File:Salve amulet detail.png|center|300x150px]]\n|-\n|[[File:Salve amulet v1.png|center]]\n|[[File:Salve amulet.png|center]]\n|-\n|21 December 2004 –<br/>8 August 2019\n|8 August 2019 –<br/>present\n|}\n\n==Trivia==\n* Contrary to the item's examine text, its strength and attack are increased by 16.67% rather than 15%.\n\n==References==\n{{Reflist}}\n\n{{Haunted Mine}}\n{{Amulets}}\n{{Attribute_affinities}}\n{{Prayer items}}\n{{Lair of Tarn Razorlor}}", "downloadedAt": "2025-09-10T00:49:02.000Z", "source": "osrs_wiki_market_intelligence_batch", "rawWikitext": true, "contentLength": 5784}