{"title": "Gem bag", "namespace": "Main", "content": "{{External|rs}}\n{{Confuse|Bag full of gems}}\n{{Infobox Item\n|version1 = Closed\n|version2 = Open\n|name1 = Gem bag\n|name2 = Open gem bag\n|image1 = [[File:Gem bag.png]]\n|image2 = [[File:Open gem bag.png]]\n|release1 = [[15 May]] [[2014]]\n|release2 = [[23 January]] [[2020]]\n|update1 = Coal & gem bags and Attack options\n|update2 = Wiki Integration\n|members = Yes\n|quest = No\n|tradeable = No\n|placeholder = Yes\n|equipable = No\n|stackable = No\n|noteable = No\n|options1 = Fill, Open, Check, Empty, Destroy\n|options2 = Fill, Close, Check, Empty, Destroy\n|destroy = You will have to buy another gem bag from Prospector Percy.\n|examine = You can carry uncut gems in here.\n|value = 100\n|weight = 1.000\n|id1 = 12020\n|id2 = 24481\n|leagueRegion = asgarnia\n}}\n{{Synced switch\n|version1 = [[File:Gem bag detail.png|left|155px]]\n|version2 = [[File:Open gem bag detail.png|left|160px]]\n}}\nThe '''gem bag''' is an item purchasable from [[Prospector Percy's Nugget Shop]] for 100 [[golden nugget]]s. It can hold 60 of each (300 total) '''uncut''' [[gems]] of the following types:\n\n*[[Uncut sapphire]]\n*[[Uncut emerald]]\n*[[Uncut ruby]]\n*[[Uncut diamond]]\n*[[Uncut dragonstone]]\n\nA full gem bag is worth {{Coins|60*({{GEP|Uncut sapphire}} + {{GEP|Uncut emerald}} + {{GEP|Uncut ruby}} + {{GEP|Uncut diamond}} + {{GEP|Uncut dragonstone}})}}. To deposit a gem, players can use the gem with the gem bag or left-click the bag to store all uncut gems in their inventory. This will produce a game message saying, {{Mes|You add the gem[s] to your bag.}}Players can empty the bag via a right-click option or by shift-clicking the bag (if they have shift-click item dropping enabled) in their inventory or whilst their bank is open. \n\nPlayers can \"open\" the bag via a right-click option. Whilst open, any gems that the player picks up off the ground or obtains during [[Mining]] and [[Thieving]] are automatically placed inside the bag, if there is enough space. Players will receive a game message confirming the open gem bag automatically stored the gem saying, {{Mes|You put it straight into your open gem bag.}}\n\nPlayers may only own one gem bag at a time. Players who no longer want their gem bag can sell it back to Percy for 80% of the original cost.\n\nOn [[death]], the gem bag does not lose its contents.\n\n==Uses==\nThe gem bag is useful in activities that involve collecting uncut gems to save inventory space and reduce bank trips. Examples of such uses are: buying gems from [[TzHaar]] gem shops; collecting gems dropped by monsters who drop gems frequently, such as [[gorak]]s and the [[Gemstone Crab]], in addition to many [[Slayer]] monsters (almost exclusively via the [[gem drop table]]); and cracking [[Wall safe (lobby)|wall safes]] in the [[Rogues' Den]]. Gems mined from [[gem rocks]], [[Motherlode Mine]], [[Daeyalt essence mine]], and regular mining (a charged [[amulet of glory]] will increase the amount of gems from mining), or stolen from [[TzHaar-Hur]], [[stone chest]]s, [[Vyre|Vyre citizens]], and [[Gem stall|gem stalls]] are automatically placed in the gem bag whilst it is open, removing the need to put them in manually.\n\nThe gem bag can also be used whilst doing [[muddy key]] runs in the [[Wilderness]] or [[crystal key]] runs in [[Taverley]] or [[Prifddinas]]. The [[muddy chest]] and the [[crystal chest]]/[[Elven Crystal Chest]] yield one [[uncut ruby]] and one [[uncut dragonstone]] respectively each time they are opened, therefore the gem bag can allow a player to carry back much more loot from the chest if they bring multiple keys.\n\n==Shop locations==\n{{Store locations list|Gem bag}}\n\n==Transcript==\n{{Hastranscript|item}}\n\n==Changes==\n{{Subject changes header}}\n{{Subject changes\n|date = 10 September 2020\n|update = A Porcine of Interest\n|change = Gems stolen from a [[gem stall]] can now go straight into a Gem Bag, provided you have one in your Inventory.\n}}\n{{Subject changes\n|date = 12 August 2020\n|update = Darker Graceful and Other Changes\n|change = The 'Empty' option on the Gem Bag has been replaced with an option that will allow you to transfer all gems to your Inventory at once, starting with sapphires.\n}}\n{{Subject changes\n|date = 25 June 2020\n|update = Death Changes\n|change = Uncut rubies looted from pickpocketing [[vyre]]s are now automatically placed into an open gem bag providing that there is enough space.\n}}\n{{Subject changes\n|date = 12 March 2020\n|update = Phosani's Nightmare and HiScores\n|change = Gems obtained from [[stone chest]]s are now automatically placed into an open gem bag providing that there is enough space.\n}}\n{{Subject changes\n|date = 5 March 2020\n|update = Phosani's Nightmare and Tinted Hitsplats\n|change = The opened variant's name was changed from \"Opened gem bag\" to \"Open gem bag\".\n}}\n{{Subject changes\n|date = 23 January 2020\n|update = Wiki Integration\n|poll = Old School Content Poll 69: Game Improvements\n|change = *Open and closed versions have been added, which allows gems that the player obtains while mining and pickpocketing [[TzHaar-Hur]] be automatically placed inside the bag, providing there is space for it.\n*\"Open\" was added as a right-click option.\n}}\n{{Subject changes\n|date = 22 February 2018\n|update = Old School RuneScape's 5th Birthday\n|poll = Quality of Life - Skilling Suggestions 2018\n|change = Emptying the contents of the gem bag into the inventory can now be done by holding down shift and left-clicking it if shift-click dropping is enabled.\n}}\n{{Subject changes\n|date = 20 November 2014\n|update = Instanced Corp & Packs\n|poll = Old School Content Poll 27\n|change = Gem bags now have an 'empty' option when you have the bank interface open, which when selected deposits the bag's entire contents into the bank.\n}}\n{{Subject changes footer}}\n\n== Trivia ==\n\n* In ''RuneScape'', the Gem bag can be purchased as a {{RSL|Dungeoneering}} reward. However, the bag holds a smaller gem capacity and cannot carry Dragonstones. ''RuneScape'' players can also purchase a {{RSL|Gem bag (upgraded)}} which the ''Old School RuneScape'' Gem bag is based on.\n{{Storage Items}}\n{{Motherlode Mine}}\n[[Category:Collection log items]]", "downloadedAt": "2025-09-10T00:50:09.000Z", "source": "osrs_wiki_market_intelligence_batch", "rawWikitext": true, "contentLength": 6104}