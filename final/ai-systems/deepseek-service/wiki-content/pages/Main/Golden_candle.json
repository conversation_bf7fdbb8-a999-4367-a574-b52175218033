{"title": "Golden candle", "namespace": "Main", "content": "{{External|rs}}\n{{otheruses|the item from the Priest in Peril quest|the construction item|Gold candlesticks}}\n{{Infobox Item\n|name = Golden candle\n|image = [[File:Golden candle.png]]\n|release = [[29 June]] [[2004]]\n|update = Priest In Peril Quest\n|members = Yes\n|quest = [[Priest in Peril]]\n|tradeable = No\n|placeholder = Yes\n|equipable = No\n|stackable = No\n|noteable = No\n|options = Drop\n|examine = A replica candle made of solid gold.\n|value = 300\n|weight = 0.028\n|id = 2947\n}}\n[[File:Golden candle detail.png|left|50px]]\nA '''golden candle''' is a [[quest items|quest item]] used only in [[Priest in Peril]]. It is obtained by using a [[candle]] on the north-west [[Monument]] in the [[Paterdomus Temple#Basement|Paterdomus mausoleum]].\n\nIt serves no purpose in the quest, and has no uses outside the quest, as it cannot be used as a [[light source]] in a dark area. However, players can cast Low Alchemy or High alchemy on it. \n\nIt cannot be obtained once [[<PERSON><PERSON><PERSON>]] has moved to the dungeon after the player has exhausted all dialogue with him after using the [[blessed water]] on the coffin.\n\n==Creation==\n<tabber>\nCandle=\n{{Recipe\n|notes = {{SCP|Quest}} Partial completion of [[Priest in Peril]]\n|members = Yes\n|ticks = \n|facilities = Monument\n|mat1 = Candle\n|output1 = Golden candle\n|output1cost = No\n}}\n|-|\nLit candle=\n{{Recipe\n|notes = {{SCP|Quest}} Partial completion of [[Priest in Peril]]\n|members = Yes\n|ticks = \n|facilities = Monument\n|mat1 = Lit candle\n|mat1cost = No\n|output1 = Golden candle\n|output1cost = No\n}}\n</tabber>\n\n==Trivia==\n*If the golden candle is used on the monument with a normal candle, the player will say: \"You know... I think I'd rather keep the valuable solid gold candle.\"\n*A [[Black candle]] cannot be used as a replacement.\n**A lit normal candle works, however.\n\n{{Priest in Peril}}\n{{Gold}}", "downloadedAt": "2025-09-10T00:50:03.000Z", "source": "osrs_wiki_market_intelligence_batch", "rawWikitext": true, "contentLength": 1831}