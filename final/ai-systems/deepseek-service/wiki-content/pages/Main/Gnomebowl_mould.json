{"title": "Gnomebowl mould", "namespace": "Main", "content": "{{External|rs}}\n{{Infobox Item\n|name = Gnomebowl mould\n|image = [[File:Gnomebowl mould.png]]\n|release = [[29 March]] [[2004]]\n|update = RS2 Launched!\n|members = Yes\n|quest = No\n|tradeable = Yes\n|placeholder = Yes\n|equipable = No\n|stackable = No\n|noteable = Yes\n|options = Drop\n|examine = A large ovenproof bowl.\n|value = 10\n|weight = 0.150\n|exchange = Yes\n|id = 2166\n}}\n[[File:Gnomebowl mould detail.png|left|130px]]\nA '''gnomebowl mould''' is an item used in [[gnome cooking]]. It can be bought in the [[Grand Tree]] by talking to [[<PERSON><PERSON>]] in the north-west corner on the {{FloorNumber|uk=1}}, or it can be acquired for free by searching the tree gnome kitchen cabinet at the northernmost part of the Grand Tree for a \"gnome bowl\".\n\nAdding [[gianne dough]] to the mould will create a [[raw gnomebowl]], the basis for [[Gnome cooking#Gnomebowls|gnomebowl cooking]].\n\n==Products==\n{{Uses material list|Gnomebowl mould}}\n\n==Item sources==\n{{Drop sources|Gnomebowl mould}}\n\n===Shop locations===\n{{Store locations list|Gnomebowl mould}}\n\n==Changes==\n{{Subject changes header}}\n{{Subject changes\n|date = 7 August 2006\n|update = Gnome Cuisine\n|change = The item's value was increased from 1 to 10.\n}}\n{{Subject changes\n|date = 29 March 2004\n|update = RS2 Launched!\n|change = The item became permanently available with the launch of ''RuneScape 2''.\n}}\n{{Subject changes\n|date = 2 February 2004\n|update = Huge Runescape 2 update\n|change = The item became obtainable in the ''RuneScape 2'' Beta.\n}}\n{{Subject changes\n|date = 1 December 2003\n|update = RuneScape 2 Beta is Here!\n|change = The item was added to the ''RuneScape 2'' Beta cache.\n}}\n{{Subject changes footer}}\n\n==Trivia==\n*In ''[[RuneScape Classic]]'', the player could form a raw gnomebowl without this item, just by selecting the \"mould\" option of gianne dough.\n\n{{Gnome cooking}}", "downloadedAt": "2025-09-10T00:49:57.000Z", "source": "osrs_wiki_market_intelligence_batch", "rawWikitext": true, "contentLength": 1835}