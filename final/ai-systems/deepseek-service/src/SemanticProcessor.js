/**
 * Semantic Processor
 * Handles embeddings and classification only (no AI summarization)
 */

const axios = require('axios');

class SemanticProcessor {
  constructor(options = {}) {
    this.ollamaUrl = 'http://localhost:11434';
    this.embeddingModel = 'nomic-embed-text:latest'; // Fast embedding model
    this.options = {
      timeout: 0, // No timeout - let AI process as long as needed
      maxRetries: 2,
      retryDelay: 1000,
      ...options
    };
  }

  async initialize() {
    // Check AI service availability
    await this.checkService();
  }

  async checkService() {
    try {
      // Check Ollama connection
      const response = await axios.get(`${this.ollamaUrl}/api/tags`, {
        timeout: 5000
      });

      // Check if embedding model is available
      const models = response.data.models || [];
      const modelExists = models.some(m => m.name === this.embeddingModel);

      if (!modelExists) {
        throw new Error(`Embedding model ${this.embeddingModel} not found in Ollama. Please run: ollama pull ${this.embeddingModel}`);
      }

      return { status: 'healthy', embeddingModel: this.embeddingModel };
    } catch (error) {
      throw new Error(`Ollama service not available: ${error.message}`);
    }
  }

  /**
   * Process content with AI semantic analysis
   */
  async process(title, content) {
    try {
      // Generate embeddings only (classification handled by EnhancedClassifier)
      const embeddings = await this.generateEmbeddings(content);

      return {
        success: true,
        data: {
          embeddings: embeddings,
          processing: {
            charactersProcessed: content.length,
            processingTime: Date.now(),
            method: 'embeddings-only'
          }
        }
      };

    } catch (error) {
      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }

  async generateEmbeddings(content) {
    try {
      // Use Ollama's embedding endpoint
      const response = await axios.post(`${this.ollamaUrl}/api/embeddings`, {
        model: this.embeddingModel,
        prompt: content.substring(0, 8000) // Reasonable limit for embeddings
      }, {
        timeout: this.options.timeout,
        headers: { 'Content-Type': 'application/json' }
      });

      return {
        vector: response.data.embedding || [],
        dimensions: response.data.embedding?.length || 0,
        model: this.embeddingModel
      };

    } catch (error) {
      console.warn(`Embedding generation failed: ${error.message}`);
      return {
        vector: [],
        dimensions: 0,
        model: 'failed'
      };
    }
  }







  /**
   * Process multiple pages in batch
   */
  async processBatch(pages) {
    const results = {};

    for (const page of pages) {
      try {
        results[page.title] = await this.process(page.title, page.content);
      } catch (error) {
        results[page.title] = {
          success: false,
          error: error.message,
          data: null
        };
      }
    }

    return results;
  }

  /**
   * Create processing summary
   */
  createProcessingSummary(results) {
    const summary = {
      totalProcessed: Object.keys(results).length,
      successful: 0,
      failed: 0,
      totalEmbeddings: 0,
      averageProcessingTime: 0
    };

    let totalProcessingTime = 0;

    Object.values(results).forEach(result => {
      if (!result.success) {
        summary.failed++;
      } else {
        summary.successful++;

        if (result.data?.embeddings?.dimensions > 0) {
          summary.totalEmbeddings++;
        }

        if (result.data?.processing?.processingTime) {
          totalProcessingTime += result.data.processing.processingTime;
        }
      }
    });

    if (summary.successful > 0) {
      summary.averageProcessingTime = totalProcessingTime / summary.successful;
    }

    return summary;
  }
}

module.exports = SemanticProcessor;
