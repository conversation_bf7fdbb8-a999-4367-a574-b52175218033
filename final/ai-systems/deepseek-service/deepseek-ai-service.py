#!/usr/bin/env python3
"""
DeepSeek R1 AI Service for OSRS Semantic Processing
Uses DeepSeek R1 for real, intelligent analysis instead of garbage templates
"""

import os
import json
import logging
from flask import Flask, request, jsonify
import requests
import time
from sentence_transformers import SentenceTransformer
import torch
import numpy as np
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Configure Flask for long-running AI requests
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0
app.config['PERMANENT_SESSION_LIFETIME'] = 3600  # 1 hour
# Remove any default timeouts - let AI process as long as needed

class DeepSeekAIService:
    def __init__(self):
        self.client = None
        self.embedding_model = None
        self.semantic_data = {}  # Wiki semantic data
        self.social_data = {}    # Social media data (Reddit, Discord, Twitter)
        self.semantic_index = {}
        self.social_embeddings = {}  # Social media embeddings
        self.stats = {
            'requests_processed': 0,
            'content_analyzed': 0,
            'embeddings_generated': 0,
            'chat_queries': 0,
            'errors': 0,
            'reddit_posts_processed': 0,
            'social_embeddings_created': 0
        }
        
    def initialize(self):
        """Initialize local Ollama client and embedding model"""
        logger.info("🚀 Initializing Local DeepSeek R1 AI Service...")

        # Initialize local Ollama client
        self.ollama_url = os.getenv('OLLAMA_URL', 'http://localhost:11434')
        self.model = os.getenv('AI_MODEL', 'deepseek-r1:8b')

        # Test Ollama connection
        try:
            import requests
            response = requests.get(f"{self.ollama_url}/api/tags", timeout=5)
            response.raise_for_status()

            # Check if our model is available
            models = response.json().get('models', [])
            model_exists = any(m.get('name') == self.model for m in models)

            if not model_exists:
                logger.error(f"❌ Model {self.model} not found in Ollama!")
                raise ValueError(f"Model {self.model} not available. Please run: ollama pull {self.model}")

            # Set client to True to indicate successful connection
            self.client = True
            logger.info(f"✅ Local Ollama connection successful ({self.model})")
        except Exception as e:
            logger.error(f"❌ Local Ollama connection failed: {e}")
            raise

        # Initialize embedding model (local)
        logger.info("Loading embedding model...")
        self.embedding_model = SentenceTransformer('sentence-transformers/all-mpnet-base-v2')
        logger.info("✅ Embedding model loaded")

        # Load semantic data for chat queries
        self.load_semantic_data()

        # Load social media data and create embeddings
        self.load_social_media_data()

        logger.info("🎉 Local DeepSeek AI Service ready!")

    def load_semantic_data(self):
        """Load semantic data for chat queries"""
        try:
            # Load semantic index
            semantic_results_path = Path("../../data-storage/semantic-results")
            index_path = semantic_results_path / "semantic-index.json"

            if index_path.exists():
                with open(index_path, 'r') as f:
                    self.semantic_index = json.load(f)
                logger.info(f"✅ Loaded semantic index with {len(self.semantic_index)} items")
            else:
                logger.warning("⚠️ No semantic index found")

            # Load ALL semantic data for complete context
            pages_path = semantic_results_path / "pages"
            if pages_path.exists():
                loaded_count = 0
                all_pages = list(pages_path.glob("*.json"))
                logger.info(f"📊 Loading ALL {len(all_pages)} semantic pages...")

                for page_file in all_pages:  # Load ALL pages, not just 1000
                    try:
                        with open(page_file, 'r') as f:
                            page_data = json.load(f)
                            title = page_data.get('title', page_file.stem)
                            self.semantic_data[title] = page_data
                            loaded_count += 1

                            # Progress indicator for large datasets
                            if loaded_count % 1000 == 0:
                                logger.info(f"   Loaded {loaded_count}/{len(all_pages)} pages...")

                    except Exception as e:
                        continue

                logger.info(f"✅ Loaded ALL {loaded_count} semantic pages for complete context")
            else:
                logger.warning("⚠️ No semantic pages found")

        except Exception as e:
            logger.error(f"Failed to load semantic data: {e}")

    def load_social_media_data(self):
        """Load social media data and create embeddings for Reddit posts"""
        try:
            # Load Reddit posts from GUI control panel data
            reddit_data_path = Path("../../gui-control-panel/reddit-posts.json")
            social_media_path = Path("../../data-storage/social-media")

            if reddit_data_path.exists():
                logger.info("📱 Loading Reddit posts for semantic analysis...")
                with open(reddit_data_path, 'r') as f:
                    reddit_posts = json.load(f)

                reddit_embeddings_path = social_media_path / "reddit" / "embeddings"
                reddit_embeddings_path.mkdir(parents=True, exist_ok=True)

                for i, post in enumerate(reddit_posts):
                    try:
                        post_id = post.get('id', f'post_{i}')
                        title = post.get('title', '')
                        content = post.get('selftext', '')
                        combined_text = f"{title}\n\n{content}".strip()

                        if combined_text:
                            # Generate embeddings for the post
                            embeddings = self.generate_embeddings(combined_text)

                            # Store post data with embeddings
                            post_data = {
                                'id': post_id,
                                'title': title,
                                'content': content,
                                'combined_text': combined_text,
                                'author': post.get('author', ''),
                                'subreddit': post.get('subreddit', ''),
                                'score': post.get('score', 0),
                                'created': post.get('created', 0),
                                'embeddings': embeddings,
                                'platform': 'reddit'
                            }

                            # Store in social data
                            self.social_data[f"reddit_{post_id}"] = post_data

                            # Save embeddings to file
                            embedding_file = reddit_embeddings_path / f"{post_id}.json"
                            with open(embedding_file, 'w') as f:
                                json.dump(post_data, f, indent=2)

                            self.stats['reddit_posts_processed'] += 1
                            self.stats['social_embeddings_created'] += 1

                    except Exception as e:
                        logger.error(f"Failed to process Reddit post {i}: {e}")
                        continue

                logger.info(f"✅ Processed {self.stats['reddit_posts_processed']} Reddit posts with embeddings")
            else:
                logger.info("📱 No Reddit posts found for processing")

        except Exception as e:
            logger.error(f"Failed to load social media data: {e}")

    def analyze_reddit_post_enhanced(self, content, author, platform, metadata, is_official_source):
        """Enhanced Reddit post analysis using DeepSeek R1 with deep OSRS market intelligence"""
        try:
            start_time = time.time()

            # Generate embeddings for the post
            post_embeddings = self.generate_embeddings(content)

            # Find relevant wiki context using embeddings - get MORE context for deeper analysis
            wiki_context = self.find_comprehensive_wiki_context(content, post_embeddings)

            # Extract key entities and game elements first
            entities = self.extract_osrs_entities(content)

            # Create focused market intelligence prompt
            prompt = f"""You are an OSRS market analyst with deep knowledge of game mechanics. Analyze this Reddit post for market impact.

IMPORTANT SLAYER MECHANICS:
- Slayer masters (like Duradel, Nieve, Konar) assign tasks to kill specific monsters
- Players can only kill certain slayer monsters when assigned the task by a slayer master
- Adding a monster to a slayer master's roster means MORE players can get that task
- More tasks = more kills = more drops entering the market = potential price decreases
- Duradel is a high-level slayer master (requires 50 Slayer and 100 Combat)

POST: {content[:1200]}

WIKI CONTEXT: {wiki_context[:2000] if wiki_context else "Limited context"}

ENTITIES: {', '.join(entities[:5]) if entities else 'None'}

Analyze this post and provide market intelligence. Focus on:

1. What is this post suggesting or discussing?
2. Is this a suggestion, complaint, or current game discussion?
3. If about slayer assignments, understand that adding monsters to slayer masters increases supply
4. What items would be affected and how?
5. What should traders know about this?
6. Which wiki pages/entities from the context are most relevant?

Respond with ONLY this JSON (no other text):
{{
    "summary": "Clear explanation of what this post is about and its market significance",
    "osrs_connections": [
        {{
            "entity": "Elder custodian stalker",
            "type": "monster",
            "relevance": "How this entity relates to the post content",
            "market_impact": "Economic implications for this entity"
        }}
    ],
    "market_impact": "Detailed analysis focusing on supply/demand changes. If about slayer assignments, explain how more players getting tasks affects drop supply and prices",
    "sentiment": "positive|negative|neutral",
    "confidence": 0.75
}}"""

            # Call DeepSeek R1 with longer timeout for complex analysis
            response = requests.post(f"{self.ollama_url}/api/generate", json={
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.1,  # Lower temperature for more analytical responses
                    "top_p": 0.9,
                    "max_tokens": 2000   # More tokens for detailed analysis
                }
            }, timeout=None)  # No timeout - let DeepSeek R1 think as long as needed

            response.raise_for_status()
            ai_response = response.json().get('response', '').strip()

            # Extract JSON from DeepSeek R1 reasoning output
            json_content = self.extract_json_from_reasoning(ai_response)

            # Parse JSON response with better error handling
            try:
                analysis = json.loads(json_content)
                # Validate required fields
                if not analysis.get('summary'):
                    raise ValueError("Missing summary field")
                logger.info(f"✅ Successfully parsed AI analysis with {len(analysis.get('osrs_connections', []))} connections")
            except Exception as parse_error:
                logger.error(f"JSON parsing failed: {parse_error}")
                logger.error(f"AI Response: {ai_response[:500]}...")
                logger.error(f"Extracted JSON: {json_content[:200]}...")
                # Enhanced fallback parsing
                analysis = self.parse_complex_ai_response(ai_response, content, entities)

            # Add processing metadata
            analysis['processing_time'] = time.time() - start_time
            analysis['wiki_context_used'] = len(wiki_context) > 0 if wiki_context else False
            analysis['entities_detected'] = len(entities)

            self.stats['content_analyzed'] += 1
            return analysis

        except Exception as e:
            logger.error(f"Enhanced Reddit analysis failed: {e}")
            # Return sophisticated fallback analysis
            return self.create_fallback_analysis(content, entities if 'entities' in locals() else [],
                                               time.time() - start_time if 'start_time' in locals() else 0, str(e))

    def extract_json_from_reasoning(self, ai_response):
        """Extract JSON from DeepSeek R1 reasoning output that includes <think> tags"""
        try:
            # DeepSeek R1 outputs reasoning in <think>...</think> tags followed by the actual response
            if '</think>' in ai_response:
                # Extract content after the closing </think> tag
                json_part = ai_response.split('</think>')[-1].strip()
            else:
                # If no think tags, use the full response
                json_part = ai_response.strip()

            # Clean up any remaining formatting
            json_part = json_part.strip()

            # If it starts with markdown code blocks, extract the JSON
            if json_part.startswith('```json'):
                json_part = json_part.replace('```json', '').replace('```', '').strip()
            elif json_part.startswith('```'):
                json_part = json_part.replace('```', '').strip()

            # Find the first { and last } to extract just the JSON object
            start_idx = json_part.find('{')
            end_idx = json_part.rfind('}')

            if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                json_part = json_part[start_idx:end_idx + 1]

            return json_part

        except Exception as e:
            logger.error(f"Failed to extract JSON from reasoning: {e}")
            return ai_response  # Return original if extraction fails

    def find_comprehensive_wiki_context(self, content, post_embeddings):
        """Find comprehensive wiki context with focus on economic data"""
        try:
            if not post_embeddings.get('vector') or not self.semantic_data:
                logger.warning("No embeddings or semantic data available")
                return ""

            post_vector = np.array(post_embeddings['vector'])
            similarities = []
            content_lower = content.lower()

            logger.info(f"🔍 Searching {len(self.semantic_data)} pages for context...")

            # Enhanced search through semantic data using existing embeddings
            for title, wiki_data in self.semantic_data.items():
                try:
                    # Use existing embeddings from the semantic data instead of generating new ones
                    if 'embeddings' in wiki_data and wiki_data['embeddings'].get('vector'):
                        wiki_vector = np.array(wiki_data['embeddings']['vector'])

                        # Calculate embedding similarity using existing embeddings
                        similarity = np.dot(post_vector, wiki_vector) / (
                            np.linalg.norm(post_vector) * np.linalg.norm(wiki_vector)
                        )

                        # Let embeddings do their job naturally - no artificial boosting
                        adjusted_similarity = similarity

                        # Create context text from available data - safely handle classification
                        classification = wiki_data.get('classification', {})
                        if not isinstance(classification, dict):
                            classification = {}

                        wiki_text = f"Title: {title}\nType: {classification.get('type', 'unknown')}\nClassification: {classification.get('method', 'unknown')}"
                        if classification.get('evidence'):
                            try:
                                evidence_list = classification['evidence']
                                if isinstance(evidence_list, list):
                                    wiki_text += f"\nEvidence: {', '.join(evidence_list)}"
                            except (TypeError, KeyError):
                                pass

                        # Try to load full wiki content for this page
                        full_wiki_content = self.load_full_wiki_content(title)
                        if full_wiki_content:
                            wiki_text = f"=== {title} ===\n{full_wiki_content[:2000]}..."  # Use first 2000 chars of full content

                        similarities.append((adjusted_similarity, title, wiki_text))

                        # Debug logging for relevant matches
                        if similarity > 0.1:
                            page_type = classification.get('type', 'unknown') if isinstance(classification, dict) else 'unknown'
                            logger.info(f"  📄 {title}: similarity={similarity:.3f}, type={page_type}, full_content={'✅' if full_wiki_content else '❌'}")

                except Exception as page_error:
                    logger.debug(f"Error processing page {title}: {page_error}")
                    continue

            # Get top 5 most relevant wiki pages for comprehensive context
            similarities.sort(reverse=True)
            context_parts = []

            logger.info(f"📊 Top 10 similarity scores:")
            for i, (similarity, title, text) in enumerate(similarities[:10]):
                logger.info(f"  {i+1}. {title}: {similarity:.3f}")

            # NEW APPROACH: If similarity search fails, use dynamic research
            for similarity, title, text in similarities[:10]:  # Include top 10 pages
                if similarity > 0.1:  # Natural similarity threshold
                    # Include more text for economic analysis
                    context_parts.append(f"=== {title} ===\n{text[:1200]}...")
                    logger.info(f"✅ Including {title} (similarity: {similarity:.3f})")

            logger.info(f"📋 Final context parts: {len(context_parts)}")

            # If no context found with similarity, try dynamic research
            if len(context_parts) == 0:
                logger.info("🔍 No similarity matches found, starting dynamic research...")
                research_context = self.dynamic_wiki_research(content)
                if research_context:
                    logger.info(f"✅ Research found context: {len(research_context)} characters")
                    return research_context
                else:
                    logger.warning("⚠️ Research also failed")

            return "\n\n".join(context_parts)

        except Exception as e:
            logger.error(f"Comprehensive wiki context search failed: {e}")
            return ""

    def dynamic_wiki_research(self, content):
        """Dynamic research system that lets AI explore wiki content"""
        try:
            logger.info("🧠 Starting AI-powered dynamic research...")

            # Extract key terms for research
            research_terms = self.extract_research_terms(content)
            logger.info(f"🎯 Research terms: {research_terms}")

            # Search for pages containing these terms
            relevant_pages = self.search_wiki_pages(research_terms)
            logger.info(f"📚 Found {len(relevant_pages)} potentially relevant pages")

            if not relevant_pages:
                return ""

            # Build research context from found pages
            research_context = []
            for page_title, relevance_score in relevant_pages[:5]:  # Top 5 most relevant
                page_content = self.get_wiki_page_content(page_title)
                if page_content:
                    research_context.append(f"=== {page_title} ===\n{page_content[:2000]}...")
                    logger.info(f"✅ Added research context: {page_title}")

            return "\n\n".join(research_context)

        except Exception as e:
            logger.error(f"Dynamic research failed: {e}")
            return ""

    def extract_research_terms(self, content):
        """Extract key terms for research from the post content"""
        import re

        content_lower = content.lower()
        terms = []

        # Split into words and filter for meaningful terms
        words = re.findall(r'\b\w+\b', content_lower)

        # Look for OSRS-specific patterns and important terms
        for word in words:
            if len(word) > 3:  # Skip very short words
                # Add words that might be OSRS-related
                if any(keyword in word for keyword in ['bot', 'ban', 'drop', 'price', 'gp', 'gold', 'farm']):
                    terms.append(word)
                elif word in ['moons', 'moon', 'raid', 'boss', 'item', 'weapon', 'armor', 'armour', 'quest']:
                    terms.append(word)
                elif len(word) > 5:  # Longer words might be item/location names
                    terms.append(word)

        # Look for multi-word terms and context clues
        if 'moons' in content_lower:
            terms.extend(['moons', 'perilous', 'raid'])
        if 'bot' in content_lower and 'ban' in content_lower:
            terms.extend(['supply', 'economy', 'market'])

        # Remove duplicates and limit
        return list(set(terms))[:8]

    def search_wiki_pages(self, search_terms):
        """Search for wiki pages containing the research terms"""
        try:
            relevant_pages = []

            for title, wiki_data in self.semantic_data.items():
                relevance_score = 0
                title_lower = title.lower()

                # Check for direct term matches in title
                for term in search_terms:
                    term_lower = term.lower()
                    if term_lower in title_lower:
                        if term_lower == title_lower.replace('_', ' '):
                            relevance_score += 100  # Exact match
                        else:
                            relevance_score += 50   # Partial match

                # Check classification and evidence for relevance
                classification = wiki_data.get('classification', {})
                if isinstance(classification, dict):
                    # Boost relevance for certain types
                    page_type = classification.get('type', '')
                    if page_type in ['quest', 'raid', 'boss', 'item', 'weapon', 'armor']:
                        relevance_score += 20

                    # Check evidence for term matches
                    evidence = classification.get('evidence', [])
                    if isinstance(evidence, list):
                        for evidence_item in evidence:
                            if isinstance(evidence_item, str):
                                for term in search_terms:
                                    if term.lower() in evidence_item.lower():
                                        relevance_score += 10

                # Add to results if relevant
                if relevance_score > 0:
                    relevant_pages.append((title, relevance_score))

            # Sort by relevance score
            relevant_pages.sort(key=lambda x: x[1], reverse=True)
            return relevant_pages[:20]  # Return top 20 matches

        except Exception as e:
            logger.error(f"Wiki search failed: {e}")
            return []

    def get_wiki_page_content(self, title):
        """Get full content of a specific wiki page"""
        try:
            # First try to load full wiki content
            full_content = self.load_full_wiki_content(title)
            if full_content:
                return full_content

            # Fallback to semantic data
            if title in self.semantic_data:
                wiki_data = self.semantic_data[title]

                # Build content from available data
                content_parts = [f"Title: {title}"]

                classification = wiki_data.get('classification', {})
                if isinstance(classification, dict):
                    content_parts.append(f"Type: {classification.get('type', 'unknown')}")
                    evidence = classification.get('evidence', [])
                    if isinstance(evidence, list) and evidence:
                        content_parts.append(f"Evidence: {', '.join(evidence[:3])}")

                # Add any understanding or content
                if 'understanding' in wiki_data:
                    content_parts.append(f"Content: {wiki_data['understanding']}")
                elif 'content' in wiki_data:
                    content_parts.append(f"Content: {wiki_data['content']}")

                return "\n".join(content_parts)

            return None

        except Exception as e:
            logger.error(f"Failed to get page content for {title}: {e}")
            return None

    def load_full_wiki_content(self, title):
        """Load full wiki content from the wiki-content directory"""
        try:
            # Clean the title for filename matching
            clean_title = title.replace('/', '_').replace(' ', '_')

            # First try the main data-storage wiki-content directory (15,031 pages)
            wiki_file_path = Path("../../data-storage/wiki-content/pages/Main") / f"{clean_title}.json"
            if wiki_file_path.exists():
                with open(wiki_file_path, 'r', encoding='utf-8') as f:
                    wiki_data = json.load(f)
                    content = wiki_data.get('content', '')
                    if content:
                        # Clean up wiki markup for better AI understanding
                        cleaned_content = self.clean_wiki_markup(content)
                        logger.debug(f"✅ Loaded main wiki content for {title}: {len(cleaned_content)} chars")
                        return cleaned_content

            # Fallback to old wiki-content directory
            old_wiki_file_path = Path("../../../old/wiki-content/pages/Main") / f"{clean_title}.json"
            if old_wiki_file_path.exists():
                with open(old_wiki_file_path, 'r', encoding='utf-8') as f:
                    wiki_data = json.load(f)
                    content = wiki_data.get('content', '')
                    if content:
                        cleaned_content = self.clean_wiki_markup(content)
                        logger.debug(f"✅ Loaded old wiki content for {title}: {len(cleaned_content)} chars")
                        return cleaned_content

            # Try alternative filename patterns
            alt_patterns = [
                f"{title}.json",  # Exact title
                f"{title.replace(' ', '_')}.json",  # Spaces to underscores
                f"{title.replace('/', '_')}.json",  # Slashes to underscores
            ]

            for pattern in alt_patterns:
                for base_path in [Path("../../data-storage/wiki-content/pages/Main"), Path("wiki-content/pages/Main"), Path("../../../old/wiki-content/pages/Main")]:
                    alt_path = base_path / pattern
                    if alt_path.exists():
                        with open(alt_path, 'r', encoding='utf-8') as f:
                            wiki_data = json.load(f)
                            content = wiki_data.get('content', '')
                            if content:
                                cleaned_content = self.clean_wiki_markup(content)
                                logger.debug(f"✅ Loaded wiki content for {title} (alt pattern): {len(cleaned_content)} chars")
                                return cleaned_content

            logger.debug(f"❌ No full wiki content found for {title}")
            return None

        except Exception as e:
            logger.debug(f"Failed to load full wiki content for {title}: {e}")
            return None

    def clean_wiki_markup(self, content):
        """Clean wiki markup to make it more readable for AI"""
        import re

        # Remove common wiki markup
        content = re.sub(r'\{\{[^}]*\}\}', '', content)  # Remove templates
        content = re.sub(r'\[\[File:[^\]]*\]\]', '', content)  # Remove file links
        content = re.sub(r'\[\[([^|\]]*)\|([^\]]*)\]\]', r'\2', content)  # Convert [[link|text]] to text
        content = re.sub(r'\[\[([^\]]*)\]\]', r'\1', content)  # Convert [[link]] to link
        content = re.sub(r"'''([^']*?)'''", r'\1', content)  # Remove bold markup
        content = re.sub(r"''([^']*?)''", r'\1', content)  # Remove italic markup
        content = re.sub(r'\n+', '\n', content)  # Collapse multiple newlines

        return content.strip()

    def fetch_wiki_page_from_api(self, title):
        """Fetch wiki page content from MediaWiki API as fallback"""
        try:
            import requests

            url = "https://oldschool.runescape.wiki/api.php"
            params = {
                'action': 'query',
                'format': 'json',
                'titles': title,
                'prop': 'extracts',
                'exintro': False,
                'explaintext': True,
                'exsectionformat': 'plain'
            }

            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()

            data = response.json()
            pages = data.get('query', {}).get('pages', {})

            for page_id, page_data in pages.items():
                if page_id != '-1':  # Page exists
                    extract = page_data.get('extract', '')
                    if extract:
                        return extract

            return None

        except Exception as e:
            logger.debug(f"Failed to fetch {title} from API: {e}")
            return None

    def extract_osrs_entities(self, content):
        """Extract OSRS-specific entities from content"""
        try:
            content_lower = content.lower()
            entities = []

            # Common OSRS entities to look for
            osrs_keywords = {
                'monsters': ['custodian stalker', 'bloodveld', 'duradel', 'nieve', 'slayer master',
                           'dragon', 'demon', 'giant', 'goblin', 'skeleton', 'zombie'],
                'items': ['bond', 'gp', 'gold', 'rune', 'dragon', 'whip', 'scimitar', 'bow',
                         'arrow', 'potion', 'food', 'ore', 'bar', 'log', 'seed'],
                'activities': ['slayer', 'combat', 'mining', 'fishing', 'woodcutting', 'farming',
                             'flipping', 'trading', 'pking', 'bossing', 'raiding'],
                'locations': ['grand exchange', 'ge', 'varrock', 'lumbridge', 'falador', 'camelot',
                            'wilderness', 'dungeon', 'cave']
            }

            for category, keywords in osrs_keywords.items():
                for keyword in keywords:
                    if keyword in content_lower:
                        entities.append(f"{keyword} ({category})")

            return entities[:10]  # Limit to top 10 entities

        except Exception as e:
            logger.error(f"Entity extraction failed: {e}")
            return []

    def parse_complex_ai_response(self, ai_response, content, entities):
        """Enhanced fallback parser for complex AI responses"""
        try:
            # Try to extract key information even if JSON is malformed
            summary = "Complex market analysis completed"
            if "speculation" in ai_response.lower():
                post_type = "speculation"
            elif "suggestion" in ai_response.lower():
                post_type = "community_discussion"
            else:
                post_type = "reality"

            # Extract confidence if mentioned
            confidence = 0.5
            if "confidence" in ai_response.lower():
                import re
                conf_match = re.search(r'confidence["\s:]*(\d+\.?\d*)', ai_response.lower())
                if conf_match:
                    confidence = min(float(conf_match.group(1)), 1.0)

            return {
                'post_type': post_type,
                'implementation_likelihood': 0.3,
                'summary': f"{summary} - Entities detected: {', '.join(entities[:3])}",
                'osrs_connections': [{'entity': entity.split('(')[0].strip(),
                                    'type': 'detected',
                                    'market_relevance': 'Requires further analysis'}
                                   for entity in entities[:3]],
                'economic_impact': {
                    'affected_items': [],
                    'supply_demand_analysis': 'Analysis parsing incomplete',
                    'player_behavior_changes': 'Requires manual review'
                },
                'market_intelligence': {
                    'short_term_effects': 'Unable to parse detailed effects',
                    'long_term_effects': 'Requires manual analysis',
                    'investment_opportunities': 'Review entities detected',
                    'risk_factors': 'JSON parsing failed - manual review needed'
                },
                'sentiment': 'neutral',
                'confidence': confidence,
                'evidence_quality': 'weak',
                'reasoning': 'Fallback analysis due to parsing issues'
            }

        except Exception as e:
            logger.error(f"Complex fallback parsing failed: {e}")
            return self.create_basic_fallback(content, entities)

    def create_fallback_analysis(self, content, entities, processing_time, error):
        """Create sophisticated fallback analysis when AI fails"""
        return {
            'post_type': 'community_discussion',
            'implementation_likelihood': 0.2,
            'summary': f'Analysis system encountered issues. Detected entities: {", ".join(entities[:3]) if entities else "None"}',
            'osrs_connections': [{'entity': entity.split('(')[0].strip(),
                                'type': 'auto_detected',
                                'market_relevance': 'Requires manual analysis'}
                               for entity in entities[:5]],
            'economic_impact': {
                'affected_items': [],
                'supply_demand_analysis': 'System error prevented detailed analysis',
                'player_behavior_changes': 'Unable to determine due to system error'
            },
            'market_intelligence': {
                'short_term_effects': 'Analysis incomplete',
                'long_term_effects': 'Analysis incomplete',
                'investment_opportunities': 'Manual review required',
                'risk_factors': f'System error: {error}'
            },
            'sentiment': 'neutral',
            'confidence': 0.1,
            'evidence_quality': 'weak',
            'reasoning': 'Fallback analysis due to system error',
            'processing_time': processing_time,
            'error': error
        }

    def find_relevant_wiki_context(self, content, post_embeddings):
        """Find relevant wiki content using embedding similarity"""
        try:
            if not post_embeddings.get('vector') or not self.semantic_data:
                return ""

            post_vector = np.array(post_embeddings['vector'])
            similarities = []

            # Compare with wiki semantic data
            for title, wiki_data in list(self.semantic_data.items())[:100]:  # Limit for performance
                if 'understanding' in wiki_data and wiki_data['understanding']:
                    wiki_text = wiki_data['understanding'][:500]  # Limit text length
                    wiki_embeddings = self.generate_embeddings(wiki_text)

                    if wiki_embeddings.get('vector'):
                        wiki_vector = np.array(wiki_embeddings['vector'])
                        similarity = np.dot(post_vector, wiki_vector) / (
                            np.linalg.norm(post_vector) * np.linalg.norm(wiki_vector)
                        )
                        similarities.append((similarity, title, wiki_text))

            # Get top 3 most similar wiki pages
            similarities.sort(reverse=True)
            context_parts = []
            for similarity, title, text in similarities[:3]:
                if similarity > 0.3:  # Minimum similarity threshold
                    context_parts.append(f"{title}: {text[:200]}...")

            return "\n\n".join(context_parts)

        except Exception as e:
            logger.error(f"Wiki context search failed: {e}")
            return ""

    def find_similar_social_posts(self, content, post_embeddings):
        """Find similar social media posts using embedding similarity"""
        try:
            if not post_embeddings.get('vector') or not self.social_data:
                return ""

            post_vector = np.array(post_embeddings['vector'])
            similarities = []

            # Compare with social media data
            for post_id, social_post in self.social_data.items():
                if social_post.get('embeddings', {}).get('vector'):
                    social_vector = np.array(social_post['embeddings']['vector'])
                    similarity = np.dot(post_vector, social_vector) / (
                        np.linalg.norm(post_vector) * np.linalg.norm(social_vector)
                    )
                    similarities.append((similarity, social_post))

            # Get top 2 most similar posts
            similarities.sort(reverse=True)
            context_parts = []
            for similarity, social_post in similarities[:2]:
                if similarity > 0.4:  # Higher threshold for social posts
                    title = social_post.get('title', '')[:100]
                    content_snippet = social_post.get('content', '')[:150]
                    context_parts.append(f"Similar post: {title} - {content_snippet}...")

            return "\n".join(context_parts)

        except Exception as e:
            logger.error(f"Social context search failed: {e}")
            return ""

    def parse_ai_response_fallback(self, ai_response, content):
        """Fallback parser when JSON parsing fails"""
        return {
            'summary': 'AI analysis completed with basic parsing',
            'osrs_connections': [],
            'market_impact': 'Unable to parse detailed analysis',
            'sentiment': 'neutral',
            'confidence': 0.3
        }

    def analyze_content(self, title, content):
        """Use DeepSeek R1 to analyze OSRS content with real intelligence"""
        try:
            # Create intelligent prompt for DeepSeek R1 with JSON format requirement
            prompt = f"""You are an expert Old School RuneScape (OSRS) analyst. Analyze this social media post about OSRS and provide a detailed JSON response.

Title: {title}
Content: {content}

You must respond with ONLY valid JSON in this exact format:
{{
    "summary": "Detailed summary of the post and its OSRS implications",
    "osrs_connections": [
        {{
            "entity": "Wiki page or OSRS entity name",
            "type": "monster|item|location|npc|quest|skill",
            "relevance": "How this entity relates to the post",
            "market_impact": "Potential economic effects"
        }}
    ],
    "market_impact": "Analysis of how this could affect OSRS economy and item prices",
    "sentiment": "positive|negative|neutral",
    "confidence": 0.85
}}

Focus on identifying specific OSRS entities mentioned or implied in the post. Be thorough in connecting the post content to relevant wiki pages and game mechanics."""

            # Call local Ollama with DeepSeek R1
            import requests
            response = requests.post(f"{self.ollama_url}/api/generate", json={
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.3,
                    "top_p": 0.9,
                    "max_tokens": 500
                }
            }, timeout=None)  # No timeout - let it think as long as needed

            response.raise_for_status()
            
            ai_analysis = response.json().get('response', '').strip()
            
            # Parse the analysis into structured format
            understanding = self.parse_deepseek_analysis(ai_analysis, title)
            
            self.stats['content_analyzed'] += 1
            return understanding
            
        except Exception as e:
            logger.error(f"Local DeepSeek analysis failed for {title}: {e}")
            self.stats['errors'] += 1
            # NO FALLBACKS - Real AI or nothing
            raise Exception(f"Local DeepSeek analysis failed: {e}")

    def answer_chat_query(self, query):
        """Answer chat queries using semantic data and DeepSeek R1"""
        try:
            self.stats['chat_queries'] += 1

            # Find relevant semantic data
            relevant_context = self.find_relevant_context(query)

            # Create strict prompt that forces AI to use only provided evidence
            prompt = f"""You are an OSRS (Old School RuneScape) knowledge assistant. You must answer ONLY using the evidence and information provided in the context below. DO NOT make up any information or add details not explicitly stated in the evidence.

Context from OSRS Knowledge Base:
{relevant_context}

User Question: {query}

CRITICAL INSTRUCTIONS:
1. ONLY use information explicitly stated in the Evidence sections above
2. If the evidence mentions an item "is an [[abyssal whip]] with..." then explain what an abyssal whip is based on that evidence
3. DO NOT invent game mechanics, drop sources, or stats not mentioned in the evidence
4. If evidence shows item classifications as "weapon", state that clearly
5. Use the semantic relationships shown to explain connections between items
6. If insufficient evidence exists, say so clearly rather than guessing

Focus your answer on:
- What the evidence directly states about the item(s)
- Item classifications and types from the data
- Relationships between items shown in the semantic data
- Only mention mechanics/sources/stats that are explicitly in the evidence

Answer based STRICTLY on the provided evidence:"""

            # Call DeepSeek R1
            response = requests.post(f"{self.ollama_url}/api/generate", json={
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.2,
                    "top_p": 0.8,
                    "max_tokens": 800
                }
            }, timeout=None)

            response.raise_for_status()

            ai_response = response.json().get('response', '').strip()

            return {
                'answer': ai_response,
                'context_used': len(relevant_context) > 0,
                'service': 'DeepSeek R1 + Semantic Data'
            }

        except Exception as e:
            logger.error(f"Chat query failed: {e}")
            raise Exception(f"Chat query failed: {e}")

    def find_relevant_context(self, query):
        """Find relevant semantic context using embeddings and comprehensive search like the original system"""
        try:
            logger.info(f"🔍 Searching through {len(self.semantic_data)} semantic pages for: {query}")

            # Generate query embedding
            query_embedding = self.generate_embeddings(query)
            query_vector = query_embedding.get('vector', [])

            relevant_items = []
            query_lower = query.lower()
            query_words = [word for word in query_lower.split() if len(word) > 2]

            # Search through ALL semantic data using classifications and evidence properly
            for title, data in self.semantic_data.items():
                title_lower = title.lower()
                relevance_score = 0

                classification = data.get('classification', {})
                content = data.get('content', {})
                raw_length = content.get('rawLength', 0) if isinstance(content, dict) else 0

                # Skip redirect pages with no content (they're useless)
                if classification.get('type') == 'redirect' and raw_length < 100:
                    continue

                # 1. Classification-based scoring (PRIMARY - this is what user wants)
                item_type = classification.get('type', '').lower()
                evidence = classification.get('evidence', [])
                evidence_text = ' '.join(evidence).lower() if evidence else ''

                # For weapon queries, prioritize weapon-classified items
                if any(word in ['weapon', 'sword', 'whip', 'dagger', 'bow'] for word in query_words):
                    if item_type == 'weapon':
                        relevance_score += 100  # High priority for weapon classification

                # 2. Evidence matching (CRITICAL - contains rich item information)
                evidence_match_score = 0
                for word in query_words:
                    if word in evidence_text:
                        evidence_match_score += 30  # Evidence is very valuable

                relevance_score += evidence_match_score

                # 3. Title matching (but lower priority than classification)
                title_match_score = 0
                for word in query_words:
                    if word in title_lower:
                        if word == title_lower.replace('_', ' '):
                            title_match_score += 80  # Exact match
                        else:
                            title_match_score += 40  # Partial match

                relevance_score += title_match_score

                # 4. Embedding similarity (for semantic relationships)
                item_embeddings = data.get('embeddings', {})
                item_vector = item_embeddings.get('vector', [])

                if query_vector and item_vector and len(query_vector) == len(item_vector):
                    similarity = self.calculate_cosine_similarity(query_vector, item_vector)
                    relevance_score += similarity * 25  # Moderate weight for embeddings

                # 5. Content quality bonus (prioritize substantial pages)
                if raw_length > 1000:
                    relevance_score += 15
                elif raw_length > 500:
                    relevance_score += 8

                # Only include items with meaningful relevance
                if relevance_score > 15:  # Higher threshold for quality
                    item_info = {
                        'title': title,
                        'type': item_type,
                        'classification': classification,
                        'evidence': evidence,
                        'score': relevance_score,
                        'content_length': raw_length,
                        'data': data
                    }
                    relevant_items.append(item_info)

            # Sort by relevance and take top results
            relevant_items.sort(key=lambda x: x['score'], reverse=True)
            top_items = relevant_items[:8]  # Top 8 most relevant

            logger.info(f"✅ Found {len(top_items)} relevant items from {len(relevant_items)} matches")

            # Build comprehensive context like the original system
            return self.build_comprehensive_context(top_items, query)

        except Exception as e:
            logger.error(f"Context search failed: {e}")
            return "Context search unavailable."

    def calculate_cosine_similarity(self, vec1, vec2):
        """Calculate cosine similarity between two vectors like the original system"""
        try:
            if not vec1 or not vec2 or len(vec1) != len(vec2):
                return 0

            dot_product = sum(a * b for a, b in zip(vec1, vec2))
            norm1 = sum(a * a for a in vec1) ** 0.5
            norm2 = sum(b * b for b in vec2) ** 0.5

            if norm1 == 0 or norm2 == 0:
                return 0

            return dot_product / (norm1 * norm2)
        except Exception:
            return 0

    def load_wiki_content(self, title):
        """Load full wiki content like the original OSRS AI system"""
        try:
            # Path to wiki content directory
            wiki_content_dir = Path(__file__).parent.parent.parent / "data-storage" / "wiki-content" / "pages" / "Main"

            # Clean title for filename matching
            clean_title = title.replace(' ', '_').replace('/', '_').replace('\\', '_').replace(':', '_').replace('*', '_').replace('?', '_').replace('"', '_').replace('<', '_').replace('>', '_').replace('|', '_')

            # Look for matching file
            wiki_file = wiki_content_dir / f"{clean_title}.json"

            if wiki_file.exists():
                with open(wiki_file, 'r', encoding='utf-8') as f:
                    wiki_data = json.load(f)
                    content = wiki_data.get('content', '')

                    # Skip redirect pages
                    if content.startswith('#REDIRECT'):
                        return None

                    return content

            return None
        except Exception as e:
            logger.debug(f"Failed to load wiki content for {title}: {e}")
            return None

    def build_comprehensive_context(self, relevant_items, query):
        """Build comprehensive context using classifications, evidence, AND full wiki content like original system"""
        try:
            if not relevant_items:
                return "No specific OSRS context found for this query."

            context_parts = ["OSRS Knowledge Base Context:\n"]

            for i, item in enumerate(relevant_items):
                context_parts.append(f"Item {i+1}: {item['title']}")
                context_parts.append(f"Type: {item['type']} (relevance: {item['score']:.1f})")

                # Add evidence (this is the most valuable information!)
                evidence = item.get('evidence', [])
                if evidence:
                    context_parts.append(f"Evidence: {' | '.join(evidence[:3])}")

                # Load full wiki content on demand (LIKE THE ORIGINAL SYSTEM!)
                wiki_content = self.load_wiki_content(item['title'])
                if wiki_content:
                    # Truncate very long content but keep substantial amount
                    if len(wiki_content) > 3000:
                        wiki_content = wiki_content[:3000] + "..."
                    context_parts.append(f"Full Wiki Content:\n{wiki_content}")

                # Add classification details
                classification = item.get('classification', {})
                if classification.get('method'):
                    context_parts.append(f"Classification Method: {classification['method']}")

                context_parts.append("")  # Empty line between items

            # Group items by type for better understanding
            weapon_items = [item for item in relevant_items if item['type'] == 'weapon']
            if weapon_items and any(word in query.lower() for word in ['weapon', 'whip', 'sword', 'dagger']):
                context_parts.append("Weapon Analysis:")
                for item in weapon_items[:3]:  # Top 3 weapons
                    evidence = item.get('evidence', [])
                    if evidence:
                        context_parts.append(f"  - {item['title']}: {evidence[0] if evidence else 'No evidence'}")
                context_parts.append("")

            # Add semantic similarity for top items
            if len(relevant_items) >= 2:
                context_parts.append("Semantic Relationships:")
                for i in range(min(2, len(relevant_items) - 1)):
                    for j in range(i + 1, min(3, len(relevant_items))):
                        item1_vec = relevant_items[i]['data'].get('embeddings', {}).get('vector', [])
                        item2_vec = relevant_items[j]['data'].get('embeddings', {}).get('vector', [])

                        if item1_vec and item2_vec:
                            similarity = self.calculate_cosine_similarity(item1_vec, item2_vec)
                            if similarity > 0.4:  # Higher threshold for meaningful relationships
                                context_parts.append(f"  - {relevant_items[i]['title']} ↔ {relevant_items[j]['title']}: {similarity*100:.1f}% similar")
                context_parts.append("")

            return "\n".join(context_parts)

        except Exception as e:
            logger.error(f"Context building failed: {e}")
            return f"Found {len(relevant_items)} relevant items but failed to build context."

    def parse_deepseek_analysis(self, analysis, title):
        """Parse DeepSeek's analysis into structured format"""
        lines = analysis.split('\n')
        
        # Extract summary (first substantial paragraph)
        summary = ""
        significance = ""
        key_points = []
        
        current_section = ""
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Look for section headers
            if any(word in line.lower() for word in ['summary', 'overview', 'description']):
                current_section = "summary"
                continue
            elif any(word in line.lower() for word in ['significance', 'importance', 'role']):
                current_section = "significance"
                continue
            elif any(word in line.lower() for word in ['mechanics', 'features', 'gameplay']):
                current_section = "mechanics"
                continue
                
            # Extract content based on section
            if current_section == "summary" and not summary:
                summary = line
            elif current_section == "significance" and not significance:
                significance = line
            elif current_section == "mechanics":
                key_points.append(line)
            elif not summary:  # First substantial line becomes summary
                summary = line
                
        # NO FALLBACKS - Real AI or nothing
        if not summary:
            raise Exception(f"AI parsing failed for {title} - no summary extracted from analysis")
        if not significance:
            raise Exception(f"AI parsing failed for {title} - no significance extracted from analysis")
            
        return {
            'summary': summary,
            'significance': significance,
            'key_points': key_points,
            'concepts': self.extract_concepts(analysis),
            'context': f"DeepSeek R1 analysis of {title}",
            'reasoning': analysis,  # Full analysis for reference
            'ai_response': analysis
        }
    
    def extract_concepts(self, analysis):
        """Extract key OSRS concepts from the analysis"""
        osrs_terms = [
            'combat', 'quest', 'skill', 'training', 'experience', 'level',
            'weapon', 'armor', 'magic', 'prayer', 'attack', 'defence',
            'strength', 'ranged', 'slayer', 'boss', 'raid', 'dungeon',
            'minigame', 'pvp', 'pvm', 'grand exchange', 'trading',
            'membership', 'f2p', 'p2p', 'wilderness', 'teleport'
        ]
        
        found_concepts = []
        analysis_lower = analysis.lower()
        
        for term in osrs_terms:
            if term in analysis_lower:
                found_concepts.append(term)
                
        return found_concepts[:10]  # Limit to top 10
    
    def generate_embeddings(self, text):
        """Generate embeddings using local model"""
        try:
            embeddings = self.embedding_model.encode(text)
            self.stats['embeddings_generated'] += 1
            
            return {
                'vector': embeddings.tolist(),
                'dimensions': len(embeddings),
                'model': 'all-mpnet-base-v2'
            }
        except Exception as e:
            logger.error(f"Embedding generation failed: {e}")
            return {
                'vector': [],
                'dimensions': 0,
                'model': 'failed'
            }

# Initialize service
service = DeepSeekAIService()

@app.route('/health', methods=['GET'])
def health():
    return jsonify({
        'status': 'healthy',
        'service': 'DeepSeek R1 AI Service',
        'models_loaded': service.client is not None and service.embedding_model is not None,
        'model_states': {
            'deepseek': 'loaded' if service.client else 'not_loaded',
            'embeddings': 'loaded' if service.embedding_model else 'not_loaded'
        },
        'stats': service.stats
    })

@app.route('/analyze-post-enhanced', methods=['POST'])
def analyze_post_enhanced():
    """Enhanced Reddit post analysis using both wiki and social media context"""
    try:
        data = request.get_json()
        content = data.get('content', '')
        author = data.get('author', '')
        platform = data.get('platform', 'reddit')
        metadata = data.get('metadata', {})
        is_official_source = data.get('is_official_source', False)

        if not content:
            return jsonify({'error': 'No content provided'}), 400

        service.stats['requests_processed'] += 1

        # Analyze post with enhanced context (wiki + social media)
        analysis = service.analyze_reddit_post_enhanced(content, author, platform, metadata, is_official_source)

        return jsonify({
            'success': True,
            'summary': analysis.get('summary', ''),
            'osrs_connections': analysis.get('osrs_connections', []),
            'market_impact': analysis.get('market_impact', ''),
            'confidence': analysis.get('confidence', 0.5),
            'sentiment': analysis.get('sentiment', 'neutral'),
            'processing_time': analysis.get('processing_time', 0),
            'timestamp': time.time(),
            'service': 'DeepSeek R1 + Wiki + Social Context'
        })

    except Exception as e:
        logger.error(f"Enhanced post analysis failed: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': time.time()
        }), 500

@app.route('/analyze', methods=['POST'])
def analyze():
    try:
        data = request.get_json()
        title = data.get('title', 'Unknown')
        content = data.get('content', '')

        if not content:
            return jsonify({'error': 'No content provided'}), 400

        service.stats['requests_processed'] += 1

        # Generate understanding using DeepSeek R1
        understanding = service.analyze_content(title, content)

        # Generate embeddings
        embeddings = service.generate_embeddings(content)

        # Extract entities (simple keyword extraction for now)
        entities = service.extract_concepts(content)
        
        return jsonify({
            'understanding': understanding,
            'embeddings': embeddings,
            'discovered_connections': entities,
            'processing_time': time.time(),
            'service': 'DeepSeek R1'
        })
        
    except Exception as e:
        logger.error(f"Analysis request failed: {e}")
        service.stats['errors'] += 1
        return jsonify({'error': str(e)}), 500

@app.route('/chat', methods=['POST'])
def chat():
    """Handle chat queries with semantic context"""
    try:
        data = request.get_json()
        query = data.get('query', '')

        if not query:
            return jsonify({'error': 'No query provided'}), 400

        # Answer using semantic data
        result = service.answer_chat_query(query)

        return jsonify({
            'response': result['answer'],
            'context_used': result['context_used'],
            'service': result['service'],
            'processing_time': time.time()
        })

    except Exception as e:
        logger.error(f"Chat request failed: {e}")
        service.stats['errors'] += 1
        return jsonify({'error': str(e)}), 500

@app.route('/stats', methods=['GET'])
def stats():
    return jsonify(service.stats)

if __name__ == '__main__':
    try:
        service.initialize()
        app.run(host='0.0.0.0', port=5001, debug=False)
    except Exception as e:
        logger.error(f"Failed to start DeepSeek AI Service: {e}")
        exit(1)
