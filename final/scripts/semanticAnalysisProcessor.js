#!/usr/bin/env node

/**
 * OSRS Semantic Analysis Processor
 * 
 * This system processes the 20,287 downloaded OSRS pages using specialized AI models:
 * - sentence-transformers/all-mpnet-base-v2 (embeddings)
 * - microsoft/DialoGPT-medium (understanding)
 * - facebook/bart-large-mnli (classification)
 * 
 * Outputs organized semantic data to folders for review before database insertion.
 */

const fs = require('fs').promises;
const path = require('path');
const http = require('http');

class OSRSSemanticProcessor {
  constructor() {
    this.wikiContentDir = path.join(process.cwd(), 'wiki-content');
    this.pagesDir = path.join(this.wikiContentDir, 'pages');
    this.semanticOutputDir = path.join(this.wikiContentDir, 'semantic-analysis');
    
    // AI Model Service
    this.aiServiceUrl = 'http://localhost:5001';
    
    // Real OSRS categories from downloaded wiki data (278 categories)
    this.osrsCategories = null; // Will be loaded from actual category files
    this.categoryData = new Map(); // Store category JSON data for semantic matching
    
    this.stats = {
      pagesProcessed: 0,
      entitiesExtracted: 0,
      relationshipsFound: 0,
      categorized: {},
      errors: 0
    };
  }

  async initialize() {
    console.log('🧠 OSRS Semantic Analysis Processor');
    console.log('===================================');
    console.log('');
    console.log('🎯 Using Specialized AI Models:');
    console.log('   • sentence-transformers/all-mpnet-base-v2 (embeddings)');
    console.log('   • microsoft/DialoGPT-medium (understanding)');
    console.log('   • facebook/bart-large-mnli (classification)');
    console.log('');
    
    // Check AI service
    const serviceReady = await this.checkAIService();
    if (!serviceReady) {
      console.log('❌ AI service not available at', this.aiServiceUrl);
      console.log('');
      console.log('🚀 Start the AI service first:');
      console.log('   cd backend/ai-models/python');
      console.log('   python3 model_service.py');
      console.log('');
      throw new Error('AI service not available');
    }
    
    console.log('✅ AI service ready');
    console.log('');
    
    // Load real OSRS categories from downloaded files
    await this.loadOSRSCategories();

    // Create output directories
    await this.createOutputDirectories();

    console.log('📁 Output directories created');
    console.log(`🏷️  Loaded ${Object.keys(this.osrsCategories).length} OSRS categories`);
    console.log('');
  }

  async checkAIService() {
    return new Promise((resolve) => {
      const req = http.get(`${this.aiServiceUrl}/health`, { timeout: 3000 }, (res) => {
        resolve(res.statusCode === 200);
      });
      req.on('error', () => resolve(false));
      req.on('timeout', () => {
        req.destroy();
        resolve(false);
      });
    });
  }

  async loadOSRSCategories() {
    console.log('🏷️  Loading real OSRS categories from downloaded files...');

    const categoryDir = path.join(this.pagesDir, 'Category');
    this.osrsCategories = {};

    try {
      const categoryFiles = await fs.readdir(categoryDir);
      const jsonFiles = categoryFiles.filter(f => f.endsWith('.json'));

      console.log(`   Found ${jsonFiles.length} category files`);

      for (const file of jsonFiles) {
        try {
          const filePath = path.join(categoryDir, file);
          const content = await fs.readFile(filePath, 'utf8');
          const categoryData = JSON.parse(content);

          // Extract category name (remove "Category:" prefix)
          const categoryName = categoryData.title.replace('Category:', '');
          const categoryKey = categoryName.toLowerCase().replace(/[^a-z0-9]/g, '_');

          // Store category info
          this.osrsCategories[categoryKey] = {
            name: categoryName,
            title: categoryData.title,
            content: categoryData.content,
            keywords: this.extractCategoryKeywords(categoryName, categoryData.content)
          };

          // Store full category data for semantic matching
          this.categoryData.set(categoryName, categoryData);

        } catch (error) {
          console.log(`⚠️ Failed to load category ${file}: ${error.message}`);
        }
      }

      console.log(`✅ Loaded ${Object.keys(this.osrsCategories).length} categories`);

    } catch (error) {
      console.log(`❌ Failed to load categories: ${error.message}`);
      // Fallback to basic categories if needed
      this.osrsCategories = {
        'weapons': { name: 'Weapons', keywords: ['weapon', 'sword', 'bow'] },
        'armor': { name: 'Armor', keywords: ['armor', 'helmet', 'shield'] },
        'items': { name: 'Items', keywords: ['item'] }
      };
    }
  }

  extractCategoryKeywords(categoryName, content) {
    const keywords = [];

    // Add category name variations
    keywords.push(categoryName.toLowerCase());
    keywords.push(...categoryName.toLowerCase().split(/[\s_-]+/));

    // Extract keywords from content
    if (content) {
      // Look for category headers and descriptions
      const headerMatch = content.match(/\{\{Categoryheader\|[^|]*\|([^|]+)\|/);
      if (headerMatch) {
        const description = headerMatch[1].replace(/\[\[([^\]]+)\]\]/g, '$1');
        keywords.push(...description.toLowerCase().split(/\s+/));
      }

      // Extract linked terms
      const linkMatches = content.match(/\[\[([^\]|]+)/g);
      if (linkMatches) {
        for (const match of linkMatches) {
          const term = match.replace('[[', '').toLowerCase();
          if (!term.startsWith('category:')) {
            keywords.push(term);
          }
        }
      }
    }

    // Remove duplicates and filter
    return [...new Set(keywords)].filter(k => k.length > 2);
  }

  async createOutputDirectories() {
    // Main semantic analysis directory
    await fs.mkdir(this.semanticOutputDir, { recursive: true });

    // Category-based directories (using real OSRS categories)
    for (const categoryKey of Object.keys(this.osrsCategories)) {
      await fs.mkdir(path.join(this.semanticOutputDir, categoryKey), { recursive: true });
    }

    // Special directories
    await fs.mkdir(path.join(this.semanticOutputDir, 'uncategorized'), { recursive: true });
    await fs.mkdir(path.join(this.semanticOutputDir, 'redirects'), { recursive: true });
    await fs.mkdir(path.join(this.semanticOutputDir, 'embeddings'), { recursive: true });
    await fs.mkdir(path.join(this.semanticOutputDir, 'relationships'), { recursive: true });
  }

  async processAllPages() {
    console.log('🔍 Scanning downloaded pages...');
    
    const namespaces = await fs.readdir(this.pagesDir);
    const validNamespaces = namespaces.filter(ns => !ns.startsWith('.'));
    
    let totalPages = 0;
    for (const namespace of validNamespaces) {
      const namespacePath = path.join(this.pagesDir, namespace);
      try {
        const stat = await fs.stat(namespacePath);
        if (stat.isDirectory()) {
          const files = await fs.readdir(namespacePath);
          const jsonFiles = files.filter(f => f.endsWith('.json'));
          totalPages += jsonFiles.length;
          console.log(`   ${namespace}: ${jsonFiles.length} pages`);
        }
      } catch (error) {
        continue;
      }
    }
    
    console.log(`📊 Total pages to process: ${totalPages}`);
    console.log('');
    console.log('🚀 Starting semantic analysis...');
    console.log('');
    
    // Process each namespace
    for (const namespace of validNamespaces) {
      await this.processNamespace(namespace);
    }
    
    // Save final statistics
    await this.saveFinalStats();
    
    console.log('');
    console.log('🎉 Semantic Analysis Complete!');
    console.log('');
    console.log('📊 Final Results:');
    console.log(`   📄 Pages Processed: ${this.stats.pagesProcessed}`);
    console.log(`   🏷️  Entities Extracted: ${this.stats.entitiesExtracted}`);
    console.log(`   🔗 Relationships Found: ${this.stats.relationshipsFound}`);
    console.log(`   ❌ Errors: ${this.stats.errors}`);
    console.log('');
    console.log('📁 Results saved to:', this.semanticOutputDir);
    console.log('');
    console.log('🎯 Next Steps:');
    console.log('   1. Review semantic analysis results');
    console.log('   2. Run database import system');
    console.log('   3. Test knowledge graph queries');
  }

  async processNamespace(namespace) {
    const namespacePath = path.join(this.pagesDir, namespace);
    
    try {
      const stat = await fs.stat(namespacePath);
      if (!stat.isDirectory()) return;
      
      const files = await fs.readdir(namespacePath);
      const jsonFiles = files.filter(f => f.endsWith('.json'));
      
      console.log(`📂 Processing ${namespace}: ${jsonFiles.length} pages`);
      
      // Process in batches
      const batchSize = 5; // Small batches for AI processing
      for (let i = 0; i < jsonFiles.length; i += batchSize) {
        const batch = jsonFiles.slice(i, i + batchSize);
        await this.processBatch(namespace, batch);
        
        // Progress update
        const progress = Math.min(i + batchSize, jsonFiles.length);
        const percent = ((progress / jsonFiles.length) * 100).toFixed(1);
        process.stdout.write(`\r   Progress: ${percent}% (${progress}/${jsonFiles.length})`);
      }
      
      console.log(''); // New line after progress
      
    } catch (error) {
      console.log(`⚠️ Failed to process namespace ${namespace}: ${error.message}`);
    }
  }

  async processBatch(namespace, files) {
    const promises = files.map(file => this.processPage(namespace, file));
    await Promise.allSettled(promises);
  }

  async processPage(namespace, filename) {
    try {
      const filePath = path.join(this.pagesDir, namespace, filename);
      const content = await fs.readFile(filePath, 'utf8');
      const pageData = JSON.parse(content);
      
      // Process with AI models
      const semanticData = await this.analyzeWithAI(pageData);
      
      if (semanticData) {
        // Categorize and save
        await this.categorizeAndSave(semanticData);
        this.stats.pagesProcessed++;
        this.stats.entitiesExtracted += semanticData.entities?.length || 0;
        this.stats.relationshipsFound += semanticData.relationships?.length || 0;
      }
      
    } catch (error) {
      this.stats.errors++;
      console.log(`⚠️ Failed to process ${namespace}/${filename}: ${error.message}`);
    }
  }

  async analyzeWithAI(pageData) {
    try {
      // Skip empty pages
      if (!pageData.content || pageData.content.length < 50) {
        return null;
      }
      
      // Handle redirects
      if (pageData.content.startsWith('#REDIRECT')) {
        const redirectTarget = this.extractRedirectTarget(pageData.content);
        return {
          title: pageData.title,
          type: 'redirect',
          redirectTarget: redirectTarget,
          entities: [],
          relationships: redirectTarget ? [{
            from: pageData.title,
            to: redirectTarget,
            type: 'redirect_to',
            confidence: 1.0
          }] : []
        };
      }
      
      // Parse wikitext
      const parsedContent = this.parseWikitext(pageData.content);
      
      // Use AI models for analysis
      const [classification, entities, relationships, embedding] = await Promise.all([
        this.classifyWithAI(pageData.title, parsedContent),
        this.extractEntitiesWithAI(pageData.title, parsedContent),
        this.extractRelationshipsWithAI(pageData.title, parsedContent),
        this.createEmbeddingWithAI(pageData.title, parsedContent)
      ]);
      
      return {
        title: pageData.title,
        namespace: pageData.namespace,
        type: classification.type,
        confidence: classification.confidence,
        entities: entities,
        relationships: relationships,
        embedding: embedding,
        parsedContent: parsedContent,
        originalContent: pageData.content
      };
      
    } catch (error) {
      console.log(`⚠️ AI analysis failed for ${pageData.title}: ${error.message}`);
      return null;
    }
  }

  extractRedirectTarget(content) {
    const match = content.match(/#REDIRECT\s*\[\[([^\]]+)\]\]/);
    return match ? match[1] : null;
  }

  parseWikitext(content) {
    // Extract key information from wikitext
    const parsed = {
      infobox: {},
      categories: [],
      links: [],
      plainText: ''
    };
    
    // Extract infobox
    const infoboxMatch = content.match(/\{\{Infobox[^}]*\|([^}]+)\}\}/s);
    if (infoboxMatch) {
      const infoboxContent = infoboxMatch[1];
      const lines = infoboxContent.split('\n');
      
      for (const line of lines) {
        const match = line.match(/\|([^=]+)=(.+)/);
        if (match) {
          const key = match[1].trim();
          const value = match[2].trim();
          parsed.infobox[key] = value;
        }
      }
    }
    
    // Extract categories
    const categoryMatches = content.match(/\[\[Category:([^\]]+)\]\]/g);
    if (categoryMatches) {
      parsed.categories = categoryMatches.map(match => 
        match.replace(/\[\[Category:([^\]]+)\]\]/, '$1')
      );
    }
    
    // Extract links
    const linkMatches = content.match(/\[\[([^\]]+)\]\]/g);
    if (linkMatches) {
      parsed.links = linkMatches.map(match => 
        match.replace(/\[\[([^\]|]+).*\]\]/, '$1')
      ).filter(link => !link.startsWith('File:') && !link.startsWith('Category:'));
    }
    
    // Extract plain text
    parsed.plainText = content
      .replace(/\{\{[^}]*\}\}/g, '')
      .replace(/\[\[[^\]]*\|([^\]]+)\]\]/g, '$1')
      .replace(/\[\[([^\]]+)\]\]/g, '$1')
      .replace(/'''([^']+)'''/g, '$1')
      .replace(/''([^']+)''/g, '$1')
      .replace(/==+([^=]+)==+/g, '$1')
      .replace(/\n+/g, ' ')
      .trim();
    
    return parsed;
  }

  // AI Model Integration Methods
  async classifyWithAI(title, parsedContent) {
    try {
      const labels = [
        'weapon', 'armor', 'consumable', 'tool', 'monster', 'npc', 'quest',
        'location', 'skill', 'spell', 'prayer', 'minigame', 'lore', 'event'
      ];

      const text = `${title}. ${parsedContent.plainText.substring(0, 500)}`;

      const result = await this.callAIService('/analyze', {
        title: title.split('.')[0], // Remove the period
        content: text
      });

      if (result && result.classification) {
        return {
          type: result.classification.type,
          confidence: result.classification.confidence
        };
      }

      return { type: 'unknown', confidence: 0.5 };

    } catch (error) {
      console.log(`⚠️ Classification failed for ${title}: ${error.message}`);
      return { type: 'unknown', confidence: 0.5 };
    }
  }

  async extractEntitiesWithAI(title, parsedContent) {
    try {
      const text = `${title}. ${parsedContent.plainText.substring(0, 1000)}`;

      const result = await this.callAIService('/analyze', {
        title: title,
        content: text
      });

      if (result && result.analysis && result.analysis.entities) {
        return result.analysis.entities;
      }

      // Fallback: extract from structured data
      const entities = [];

      // From infobox
      for (const [key, value] of Object.entries(parsedContent.infobox)) {
        entities.push({
          type: key,
          value: value,
          confidence: 0.8,
          source: 'infobox'
        });
      }

      // From categories
      for (const category of parsedContent.categories) {
        entities.push({
          type: 'category',
          value: category,
          confidence: 0.9,
          source: 'category'
        });
      }

      return entities;

    } catch (error) {
      console.log(`⚠️ Entity extraction failed for ${title}: ${error.message}`);
      return [];
    }
  }

  async extractRelationshipsWithAI(title, parsedContent) {
    try {
      const relationships = [];

      // Links represent relationships
      for (const link of parsedContent.links) {
        relationships.push({
          from: title,
          to: link,
          type: 'references',
          confidence: 0.7,
          source: 'wiki_link'
        });
      }

      // Category relationships
      for (const category of parsedContent.categories) {
        relationships.push({
          from: title,
          to: category,
          type: 'belongs_to_category',
          confidence: 0.9,
          source: 'category'
        });
      }

      // Try AI understanding for implicit relationships
      const text = `${title}. ${parsedContent.plainText.substring(0, 800)}`;

      const result = await this.callAIService('/analyze', {
        title: title,
        content: text
      });

      if (result && result.analysis && result.analysis.relationships) {
        relationships.push(...result.analysis.relationships);
      }

      return relationships;

    } catch (error) {
      console.log(`⚠️ Relationship extraction failed for ${title}: ${error.message}`);
      return [];
    }
  }

  async createEmbeddingWithAI(title, parsedContent) {
    try {
      const text = `${title}. ${parsedContent.plainText.substring(0, 512)}`;

      const result = await this.callAIService('/embeddings', {
        texts: [text]
      });

      if (result && result.embeddings && result.embeddings[0]) {
        return result.embeddings[0];
      }

      return [];

    } catch (error) {
      console.log(`⚠️ Embedding creation failed for ${title}: ${error.message}`);
      return [];
    }
  }

  async callAIService(endpoint, data) {
    return new Promise((resolve, reject) => {
      const postData = JSON.stringify(data);

      const options = {
        hostname: 'localhost',
        port: 5001,
        path: endpoint,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Content-Length': Buffer.byteLength(postData)
        },
        timeout: 0  // No timeout - let AI process as long as needed
      };

      const req = http.request(options, (res) => {
        let responseData = '';

        res.on('data', (chunk) => {
          responseData += chunk;
        });

        res.on('end', () => {
          try {
            const result = JSON.parse(responseData);
            resolve(result);
          } catch (error) {
            reject(new Error(`Invalid JSON response: ${responseData}`));
          }
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });

      req.write(postData);
      req.end();
    });
  }

  async categorizeAndSave(semanticData) {
    // Determine category based on content
    const category = this.determineCategory(semanticData);
    
    // Update stats
    if (!this.stats.categorized[category]) {
      this.stats.categorized[category] = 0;
    }
    this.stats.categorized[category]++;
    
    // Save to appropriate directory
    const categoryDir = path.join(this.semanticOutputDir, category);
    const filename = `${semanticData.title.replace(/[^a-zA-Z0-9]/g, '_')}.json`;
    const filePath = path.join(categoryDir, filename);
    
    await fs.writeFile(filePath, JSON.stringify(semanticData, null, 2));
  }

  determineCategory(semanticData) {
    const title = semanticData.title.toLowerCase();

    // Handle redirects
    if (semanticData.type === 'redirect') return 'redirects';

    // Multi-level categorization using real OSRS categories
    const matches = [];

    // 1. Direct keyword matching with downloaded categories
    for (const [categoryKey, categoryInfo] of Object.entries(this.osrsCategories)) {
      let score = 0;

      // Check title against category keywords
      for (const keyword of categoryInfo.keywords) {
        if (title.includes(keyword)) {
          score += keyword.length; // Longer keywords get higher scores
        }
      }

      // Check parsed content categories (from wikitext [[Category:...]])
      if (semanticData.parsedContent && semanticData.parsedContent.categories) {
        for (const pageCategory of semanticData.parsedContent.categories) {
          if (pageCategory.toLowerCase().includes(categoryInfo.name.toLowerCase()) ||
              categoryInfo.name.toLowerCase().includes(pageCategory.toLowerCase())) {
            score += 10; // High score for direct category matches
          }
        }
      }

      // Check AI classification results
      if (semanticData.type && categoryInfo.keywords.includes(semanticData.type)) {
        score += 5;
      }

      if (score > 0) {
        matches.push({ category: categoryKey, score: score, name: categoryInfo.name });
      }
    }

    // 2. Use semantic matching from category JSONs
    if (matches.length === 0 && semanticData.parsedContent && semanticData.parsedContent.categories) {
      for (const pageCategory of semanticData.parsedContent.categories) {
        const categoryKey = pageCategory.toLowerCase().replace(/[^a-z0-9]/g, '_');
        if (this.osrsCategories[categoryKey]) {
          matches.push({
            category: categoryKey,
            score: 8,
            name: this.osrsCategories[categoryKey].name
          });
        }
      }
    }

    // 3. Return best match or uncategorized
    if (matches.length > 0) {
      // Sort by score and return highest
      matches.sort((a, b) => b.score - a.score);
      return matches[0].category;
    }

    return 'uncategorized';
  }

  async saveFinalStats() {
    const statsPath = path.join(this.semanticOutputDir, 'analysis_stats.json');
    await fs.writeFile(statsPath, JSON.stringify(this.stats, null, 2));
  }
}

async function main() {
  const processor = new OSRSSemanticProcessor();
  
  try {
    await processor.initialize();
    
    console.log('Press ENTER to start semantic analysis, or Ctrl+C to cancel...');
    await new Promise(resolve => {
      process.stdin.once('data', resolve);
    });
    
    await processor.processAllPages();
    
  } catch (error) {
    console.error('💥 Semantic analysis failed:', error);
    process.exit(1);
  }
}

main();
