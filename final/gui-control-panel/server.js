const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs').promises;
const { spawn } = require('child_process');

class OSRSControlPanelAPI {
  constructor() {
    this.app = express();
    this.port = 3001;
    
    // System processes
    this.processes = {
      dataCollection: null,
      aiChat: null,
      osrsAIQuery: null,
      deepseekService: null,
      semanticService: null,
      wikiWatcher: null,
      socialWatchers: null
    };

    // System status
    this.status = {
      dataCollection: 'stopped',
      aiChat: 'stopped',
      osrsAIQuery: 'stopped',
      deepseekService: 'stopped',
      semanticService: 'stopped',
      wikiWatcher: 'stopped',
      socialWatchers: 'stopped',
      lastUpdate: new Date()
    };
    
    this.setupMiddleware();
    this.setupRoutes();
  }

  setupMiddleware() {
    this.app.use(cors());
    this.app.use(express.json());
    this.app.use(express.static(path.join(__dirname, 'public')));
  }

  setupRoutes() {
    // System Control Routes
    this.app.post('/api/system/start-data-collection', this.startDataCollection.bind(this));
    this.app.post('/api/system/stop-data-collection', this.stopDataCollection.bind(this));
    this.app.post('/api/system/start-ai-chat', this.startAIChat.bind(this));
    this.app.post('/api/system/stop-ai-chat', this.stopAIChat.bind(this));
    this.app.post('/api/system/start-deepseek', this.startDeepSeekService.bind(this));
    this.app.post('/api/system/check-ollama', this.checkOllama.bind(this));
    this.app.post('/api/system/start-all', this.startAllSystems.bind(this));
    this.app.post('/api/system/stop-all', this.stopAllSystems.bind(this));
    this.app.get('/api/system/status', this.getSystemStatus.bind(this));

    // Social Media Routes
    this.app.get('/api/social/reddit/posts', this.getRedditPosts.bind(this));
    this.app.get('/api/social/discord/messages', this.getDiscordMessages.bind(this));
    this.app.get('/api/social/twitter/posts', this.getTwitterPosts.bind(this));
    this.app.post('/api/social/analyze-post', this.analyzePost.bind(this));
    this.app.post('/api/social/analyze-post-enhanced', this.analyzePostEnhanced.bind(this));
    this.app.get('/api/social/stats', this.getSocialStats.bind(this));
    this.app.get('/api/social/reddit/enhanced', this.getEnhancedRedditPosts.bind(this));
    this.app.post('/api/social/batch-analyze', this.batchAnalyzePosts.bind(this));

    // Data Routes
    this.app.get('/api/posts', this.getPosts.bind(this));
    this.app.get('/api/posts/:id', this.getPostDetail.bind(this));
    this.app.get('/api/stats', this.getStats.bind(this));

    // AI Chat Routes
    this.app.post('/api/ai/chat', this.chatWithAI.bind(this));
    this.app.get('/api/ai/history', this.getChatHistory.bind(this));

    // Serve GUI
    this.app.get('/', (req, res) => {
      res.sendFile(path.join(__dirname, 'public', 'index.html'));
    });
  }

  async startDataCollectionInternal() {
    // Start Wiki Watcher
    if (!this.processes.wikiWatcher) {
      console.log('📚 Starting OSRS Wiki Watcher...');
      const wikiWatcherProcess = spawn('node', ['startWatcher.js'], {
        cwd: path.join(__dirname, '../ai-systems/watcher-system'),
        stdio: ['pipe', 'pipe', 'pipe']
      });

      this.processes.wikiWatcher = wikiWatcherProcess;
      this.status.wikiWatcher = 'running';

      wikiWatcherProcess.stdout.on('data', (data) => {
        console.log(`[Wiki Watcher] ${data.toString()}`);
      });

      wikiWatcherProcess.stderr.on('data', (data) => {
        console.error(`[Wiki Watcher Error] ${data.toString()}`);
      });

      wikiWatcherProcess.on('close', (code) => {
        console.log(`Wiki Watcher process exited with code ${code}`);
        this.processes.wikiWatcher = null;
        this.status.wikiWatcher = 'stopped';
      });
    }

    // Start Social Media Watchers (Reddit Collection)
    if (!this.processes.socialWatchers) {
      console.log('📱 Starting Social Media Watchers...');
      const socialWatchersProcess = spawn('node', ['testProperRedditCollection.js'], {
        cwd: path.join(__dirname, '../reddit-collection-system/test'),
        env: {
          ...process.env,
          REDDIT_CLIENT_ID: process.env.REDDIT_CLIENT_ID,
          REDDIT_CLIENT_SECRET: process.env.REDDIT_CLIENT_SECRET
        },
        stdio: ['pipe', 'pipe', 'pipe']
      });

      this.processes.socialWatchers = socialWatchersProcess;
      this.status.socialWatchers = 'running';

      socialWatchersProcess.stdout.on('data', (data) => {
        console.log(`[Social Watchers] ${data.toString()}`);
      });

      socialWatchersProcess.stderr.on('data', (data) => {
        console.error(`[Social Watchers Error] ${data.toString()}`);
      });

      socialWatchersProcess.on('close', (code) => {
        console.log(`Social Watchers process exited with code ${code}`);
        this.processes.socialWatchers = null;
        this.status.socialWatchers = 'stopped';
      });
    }

    this.status.dataCollection = 'running';
    this.status.lastUpdate = new Date();
  }

  async startAISystemsInternal() {
    // Start DeepSeek Service
    await this.startDeepSeekService();

    // Start OSRS AI Query System
    if (!this.processes.osrsAIQuery) {
      console.log('🧠 Starting OSRS AI Query System...');
      const osrsAIProcess = spawn('node', ['osrsAIQuery.js'], {
        cwd: path.join(__dirname, '../ai-systems/osrs-ai-query'),
        env: {
          ...process.env,
          OLLAMA_URL: 'http://localhost:11434',
          AI_MODEL: 'deepseek-r1:8b'
        },
        stdio: ['pipe', 'pipe', 'pipe']
      });

      this.processes.osrsAIQuery = osrsAIProcess;
      this.status.osrsAIQuery = 'running';

      osrsAIProcess.stdout.on('data', (data) => {
        console.log(`[OSRS AI] ${data.toString()}`);
      });

      osrsAIProcess.stderr.on('data', (data) => {
        console.error(`[OSRS AI Error] ${data.toString()}`);
      });

      osrsAIProcess.on('close', (code) => {
        console.log(`OSRS AI Query process exited with code ${code}`);
        this.processes.osrsAIQuery = null;
        this.status.osrsAIQuery = 'stopped';
      });
    }

    this.status.aiChat = 'running';
    this.status.lastUpdate = new Date();
  }

  async startDataCollection(req, res) {
    try {
      if (this.processes.dataCollection) {
        return res.json({ success: false, message: 'Data collection already running' });
      }

      await this.startDataCollectionInternal();

      res.json({
        success: true,
        message: 'Data collection started successfully (Wiki Watcher + Social Media Watchers)'
      });

    } catch (error) {
      console.error('Failed to start data collection:', error);
      res.status(500).json({
        success: false,
        message: `Failed to start data collection: ${error.message}`
      });
    }
  }

  async stopDataCollection(req, res) {
    try {
      if (!this.processes.dataCollection) {
        return res.json({ success: false, message: 'Data collection not running' });
      }

      console.log('🛑 Stopping data collection system...');
      
      this.processes.dataCollection.kill('SIGTERM');
      this.processes.dataCollection = null;
      this.status.dataCollection = 'stopped';
      this.status.lastUpdate = new Date();

      res.json({ 
        success: true, 
        message: 'Data collection stopped successfully' 
      });

    } catch (error) {
      console.error('Failed to stop data collection:', error);
      res.status(500).json({ 
        success: false, 
        message: `Failed to stop data collection: ${error.message}` 
      });
    }
  }

  async startAIChat(req, res) {
    try {
      if (this.processes.osrsAIQuery) {
        return res.json({ success: false, message: 'OSRS AI Chat already running' });
      }

      console.log('🧠 Starting OSRS AI Query System...');

      // Start the OSRS AI Query system
      const osrsAIProcess = spawn('node', ['osrsAIQuery.js'], {
        cwd: path.join(__dirname, '../ai-systems/osrs-ai-query'),
        env: {
          ...process.env,
          OLLAMA_URL: 'http://localhost:11434',
          AI_MODEL: 'deepseek-r1:8b'
        },
        stdio: ['pipe', 'pipe', 'pipe']
      });

      this.processes.osrsAIQuery = osrsAIProcess;
      this.status.aiChat = 'running';
      this.status.osrsAIQuery = 'running';
      this.status.lastUpdate = new Date();

      osrsAIProcess.stdout.on('data', (data) => {
        console.log(`[OSRS AI] ${data.toString()}`);
      });

      osrsAIProcess.stderr.on('data', (data) => {
        console.error(`[OSRS AI Error] ${data.toString()}`);
      });

      osrsAIProcess.on('close', (code) => {
        console.log(`OSRS AI Query process exited with code ${code}`);
        this.processes.osrsAIQuery = null;
        this.status.aiChat = 'stopped';
        this.status.osrsAIQuery = 'stopped';
        this.status.lastUpdate = new Date();
      });

      // Also start DeepSeek service if not running
      await this.startDeepSeekService();

      res.json({
        success: true,
        message: 'OSRS AI Chat system started successfully',
        pid: osrsAIProcess.pid
      });

    } catch (error) {
      console.error('Failed to start AI chat:', error);
      res.status(500).json({
        success: false,
        message: `Failed to start AI chat: ${error.message}`
      });
    }
  }

  async stopAIChat(req, res) {
    try {
      console.log('🛑 Stopping AI Chat systems...');

      // Stop OSRS AI Query system
      if (this.processes.osrsAIQuery) {
        this.processes.osrsAIQuery.kill('SIGTERM');
        this.processes.osrsAIQuery = null;
      }

      // Stop DeepSeek service
      if (this.processes.deepseekService) {
        this.processes.deepseekService.kill('SIGTERM');
        this.processes.deepseekService = null;
      }

      this.status.aiChat = 'stopped';
      this.status.osrsAIQuery = 'stopped';
      this.status.deepseekService = 'stopped';
      this.status.lastUpdate = new Date();

      res.json({
        success: true,
        message: 'AI Chat systems stopped successfully'
      });

    } catch (error) {
      console.error('Failed to stop AI chat:', error);
      res.status(500).json({
        success: false,
        message: `Failed to stop AI chat: ${error.message}`
      });
    }
  }

  async startDeepSeekService() {
    try {
      // Check if DeepSeek service is already available
      const axios = require('axios');
      try {
        const response = await axios.get('http://localhost:5001/health', { timeout: 5000 });
        if (response.status === 200) {
          console.log('✅ DeepSeek service already running and available');
          this.status.deepseekService = 'running';
          return;
        }
      } catch (healthCheckError) {
        console.log('🤖 DeepSeek service not available, starting new instance...');
      }

      if (this.processes.deepseekService) {
        return; // Already running
      }

      console.log('🤖 Starting DeepSeek AI Service...');

      const deepseekProcess = spawn('python3', ['deepseek-ai-service.py'], {
        cwd: path.join(__dirname, '../ai-systems/deepseek-service'),
        env: {
          ...process.env,
          OLLAMA_URL: 'http://localhost:11434',
          AI_MODEL: 'deepseek-r1:8b'
        },
        stdio: ['pipe', 'pipe', 'pipe']
      });

      this.processes.deepseekService = deepseekProcess;
      this.status.deepseekService = 'running';

      deepseekProcess.stdout.on('data', (data) => {
        console.log(`[DeepSeek Service] ${data.toString()}`);
      });

      deepseekProcess.stderr.on('data', (data) => {
        console.error(`[DeepSeek Service Error] ${data.toString()}`);
      });

      deepseekProcess.on('close', (code) => {
        console.log(`DeepSeek service exited with code ${code}`);
        this.processes.deepseekService = null;
        this.status.deepseekService = 'stopped';
      });

      // Wait a moment for service to start
      await new Promise(resolve => setTimeout(resolve, 3000));

    } catch (error) {
      console.error('Failed to start DeepSeek service:', error);
    }
  }

  async checkOllama(req, res) {
    try {
      const axios = require('axios');

      // Check if Ollama is running
      const response = await axios.get('http://localhost:11434/api/tags', { timeout: 5000 });
      const models = response.data.models || [];

      // Check if DeepSeek R1 model is available
      const deepseekModel = models.find(m => m.name.includes('deepseek-r1'));

      res.json({
        success: true,
        ollama: {
          running: true,
          models: models.length,
          deepseekAvailable: !!deepseekModel,
          deepseekModel: deepseekModel ? deepseekModel.name : null
        }
      });

    } catch (error) {
      res.json({
        success: false,
        ollama: {
          running: false,
          error: error.message,
          suggestion: 'Please start Ollama: ollama serve'
        }
      });
    }
  }

  async startAllSystems(req, res) {
    try {
      console.log('🚀 Starting all OSRS Intelligence systems...');
      const results = [];

      // Start Data Collection (Wiki Watcher + Social Media Watchers)
      try {
        await this.startDataCollectionInternal();
        results.push({ system: 'Data Collection', status: 'started', success: true });
      } catch (error) {
        results.push({ system: 'Data Collection', status: 'failed', success: false, error: error.message });
      }

      // Start AI Systems (DeepSeek + OSRS AI Query)
      try {
        await this.startAISystemsInternal();
        results.push({ system: 'AI Systems', status: 'started', success: true });
      } catch (error) {
        results.push({ system: 'AI Systems', status: 'failed', success: false, error: error.message });
      }

      const allSuccessful = results.every(r => r.success);

      res.json({
        success: allSuccessful,
        message: allSuccessful ? 'All systems started successfully' : 'Some systems failed to start',
        results: results
      });

    } catch (error) {
      console.error('Failed to start all systems:', error);
      res.status(500).json({
        success: false,
        message: `Failed to start all systems: ${error.message}`
      });
    }
  }

  async stopAllSystems(req, res) {
    try {
      console.log('🛑 Stopping all OSRS Intelligence systems...');
      const results = [];

      // Stop all processes
      const processNames = Object.keys(this.processes);
      for (const processName of processNames) {
        if (this.processes[processName]) {
          try {
            this.processes[processName].kill('SIGTERM');
            this.processes[processName] = null;
            this.status[processName] = 'stopped';
            results.push({ system: processName, status: 'stopped', success: true });
          } catch (error) {
            results.push({ system: processName, status: 'failed', success: false, error: error.message });
          }
        }
      }

      this.status.lastUpdate = new Date();

      res.json({
        success: true,
        message: 'All systems stopped successfully',
        results: results
      });

    } catch (error) {
      console.error('Failed to stop all systems:', error);
      res.status(500).json({
        success: false,
        message: `Failed to stop all systems: ${error.message}`
      });
    }
  }

  async getSystemStatus(req, res) {
    try {
      // Check if Ollama is running
      let ollamaStatus = 'stopped';
      try {
        const axios = require('axios');
        await axios.get('http://localhost:11434/api/tags', { timeout: 2000 });
        ollamaStatus = 'running';
      } catch (error) {
        ollamaStatus = 'stopped';
      }

      res.json({
        success: true,
        status: this.status,
        processes: {
          dataCollection: this.processes.dataCollection ? 'running' : 'stopped',
          aiChat: this.status.aiChat,
          osrsAIQuery: this.status.osrsAIQuery,
          deepseekService: this.status.deepseekService,
          ollama: ollamaStatus
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  async getPosts(req, res) {
    try {
      const { page = 1, limit = 50, source = 'all' } = req.query;
      
      // Read posts from data directory
      const dataPath = path.join(__dirname, '../reddit-collection-system/data/reddit');
      const posts = [];

      // Read from both raw and processed directories
      const directories = ['raw', 'processed'];
      
      for (const dir of directories) {
        try {
          const dirPath = path.join(dataPath, dir);
          const files = await fs.readdir(dirPath);
          const jsonFiles = files.filter(f => f.endsWith('.json')).sort().reverse();

          for (const file of jsonFiles.slice(0, 10)) { // Limit files to prevent overload
            const filePath = path.join(dirPath, file);
            const content = await fs.readFile(filePath, 'utf8');
            const data = JSON.parse(content);
            
            if (data.data && Array.isArray(data.data)) {
              data.data.forEach(post => {
                posts.push({
                  ...post,
                  dataType: dir,
                  collectionFile: file
                });
              });
            }
          }
        } catch (error) {
          console.warn(`Could not read ${dir} directory:`, error.message);
        }
      }

      // Sort by creation date (newest first)
      posts.sort((a, b) => new Date(b.created_date || b.collection_timestamp) - new Date(a.created_date || a.collection_timestamp));

      // Pagination
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + parseInt(limit);
      const paginatedPosts = posts.slice(startIndex, endIndex);

      res.json({
        success: true,
        posts: paginatedPosts,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: posts.length,
          pages: Math.ceil(posts.length / limit)
        }
      });

    } catch (error) {
      console.error('Failed to get posts:', error);
      res.status(500).json({ 
        success: false, 
        message: error.message 
      });
    }
  }

  async getPostDetail(req, res) {
    try {
      const { id } = req.params;
      
      // Search for post in data files
      const dataPath = path.join(__dirname, '../reddit-collection-system/data/reddit');
      const directories = ['raw', 'processed'];
      
      for (const dir of directories) {
        try {
          const dirPath = path.join(dataPath, dir);
          const files = await fs.readdir(dirPath);
          const jsonFiles = files.filter(f => f.endsWith('.json'));

          for (const file of jsonFiles) {
            const filePath = path.join(dirPath, file);
            const content = await fs.readFile(filePath, 'utf8');
            const data = JSON.parse(content);
            
            if (data.data && Array.isArray(data.data)) {
              const post = data.data.find(p => p.id === id);
              if (post) {
                return res.json({
                  success: true,
                  post: {
                    ...post,
                    dataType: dir,
                    collectionFile: file
                  }
                });
              }
            }
          }
        } catch (error) {
          console.warn(`Could not search ${dir} directory:`, error.message);
        }
      }

      res.status(404).json({
        success: false,
        message: 'Post not found'
      });

    } catch (error) {
      console.error('Failed to get post detail:', error);
      res.status(500).json({ 
        success: false, 
        message: error.message 
      });
    }
  }

  async getStats(req, res) {
    try {
      // Calculate stats from stored data
      const dataPath = path.join(__dirname, '../reddit-collection-system/data/reddit');
      let totalPosts = 0;
      let aiProcessedPosts = 0;
      let financiallyRelevantPosts = 0;
      const subredditCounts = {};
      
      const directories = ['raw', 'processed'];
      
      for (const dir of directories) {
        try {
          const dirPath = path.join(dataPath, dir);
          const files = await fs.readdir(dirPath);
          const jsonFiles = files.filter(f => f.endsWith('.json'));

          for (const file of jsonFiles) {
            const filePath = path.join(dirPath, file);
            const content = await fs.readFile(filePath, 'utf8');
            const data = JSON.parse(content);
            
            if (data.data && Array.isArray(data.data)) {
              totalPosts += data.data.length;
              
              data.data.forEach(post => {
                // Count by subreddit
                const subreddit = post.subreddit;
                subredditCounts[subreddit] = (subredditCounts[subreddit] || 0) + 1;
                
                // Count AI processed
                if (post.ai_analysis && post.ai_analysis.processed) {
                  aiProcessedPosts++;
                  
                  if (post.ai_analysis.analysis && 
                      post.ai_analysis.analysis.financial_relevance && 
                      post.ai_analysis.analysis.financial_relevance.is_relevant) {
                    financiallyRelevantPosts++;
                  }
                }
              });
            }
          }
        } catch (error) {
          console.warn(`Could not read ${dir} directory:`, error.message);
        }
      }

      res.json({
        success: true,
        stats: {
          totalPosts,
          aiProcessedPosts,
          financiallyRelevantPosts,
          subredditCounts,
          systemStatus: this.status
        }
      });

    } catch (error) {
      console.error('Failed to get stats:', error);
      res.status(500).json({ 
        success: false, 
        message: error.message 
      });
    }
  }

  async chatWithAI(req, res) {
    try {
      const { message } = req.body;

      if (!message) {
        return res.status(400).json({
          success: false,
          message: 'Message is required'
        });
      }

      // Check if AI systems are running
      if (this.status.aiChat !== 'running') {
        return res.status(400).json({
          success: false,
          message: 'AI Chat system is not running. Please start it first.'
        });
      }

      console.log(`💬 Processing AI chat message: ${message.substring(0, 50)}...`);

      // Try to use DeepSeek service first
      try {
        const axios = require('axios');
        const deepseekResponse = await axios.post('http://localhost:5001/chat', {
          query: message
        }, { timeout: 0 }); // No timeout - let AI think as long as needed

        if (deepseekResponse.data && deepseekResponse.data.response) {
          // Clean up the response by removing <think> blocks and formatting properly
          let cleanResponse = deepseekResponse.data.response;

          // Remove <think>...</think> blocks
          cleanResponse = cleanResponse.replace(/<think>[\s\S]*?<\/think>/g, '');

          // Clean up extra whitespace and newlines
          cleanResponse = cleanResponse.trim();

          // If response is empty after cleaning, provide a fallback
          if (!cleanResponse) {
            cleanResponse = "I couldn't provide a clear answer to that question. Please try rephrasing your query.";
          }

          return res.json({
            success: true,
            response: cleanResponse,
            service: deepseekResponse.data.service || 'DeepSeek R1 + Semantic Data',
            context_used: deepseekResponse.data.context_used || false
          });
        }

      } catch (error) {
        console.error('DeepSeek service failed:', error.message);
        return res.status(500).json({
          success: false,
          message: 'DeepSeek AI service is unavailable. Please ensure the service is running properly.',
          error: error.message
        });
      }

    } catch (error) {
      console.error('AI chat failed:', error);
      res.status(500).json({
        success: false,
        message: error.message
      });
    }
  }

  async getChatHistory(req, res) {
    try {
      // TODO: Implement chat history storage/retrieval
      res.json({
        success: true,
        history: []
      });
    } catch (error) {
      res.status(500).json({ 
        success: false, 
        message: error.message 
      });
    }
  }

  // Social Media Methods
  async getRedditPosts(req, res) {
    try {
      const redditDataPath = path.join(__dirname, '../reddit-collection-system/data/reddit/posts');
      const files = await fs.readdir(redditDataPath);
      const jsonFiles = files.filter(f => f.endsWith('.json')).slice(0, 50);

      const posts = [];
      for (const file of jsonFiles) {
        try {
          const content = await fs.readFile(path.join(redditDataPath, file), 'utf8');
          const fileData = JSON.parse(content);

          // Handle the data structure where posts are in a 'data' array
          const postsArray = fileData.data || [fileData];

          for (const post of postsArray) {
            posts.push({
              id: post.id,
              title: post.title,
              author: post.author,
              score: post.upvotes || post.score || 0,
              created: post.created_utc,
              url: post.url,
              selftext: post.content || post.selftext || '',
              subreddit: post.subreddit,
              num_comments: post.comments || post.num_comments || 0,
              flair: post.flair
            });
          }
        } catch (e) {
          console.error(`Error reading ${file}:`, e.message);
        }
      }

      res.json({ posts: posts.sort((a, b) => b.created - a.created) });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getDiscordMessages(req, res) {
    try {
      // Placeholder for Discord messages
      res.json({
        messages: [],
        status: 'Discord integration coming soon',
        placeholder: true
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getTwitterPosts(req, res) {
    try {
      // Placeholder for Twitter posts
      res.json({
        posts: [],
        status: 'Twitter integration coming soon',
        placeholder: true
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async analyzePost(req, res) {
    try {
      const { content, platform, author, metadata } = req.body;

      // Detect if this is from a Jagex mod or official source
      const isOfficialSource = this.detectOfficialSource(author, content, platform);

      // Enhanced AI analysis with context-aware prompts
      let analysisPrompt;

      if (isOfficialSource) {
        analysisPrompt = `
🔥 OFFICIAL JAGEX MOD RESPONSE DETECTED! 🔥

Platform: ${platform}
Author: ${author} (OFFICIAL SOURCE)
Content: ${content}

This is a response from a Jagex moderator or official source. Analyze this for:

1. OFFICIAL ANNOUNCEMENT TYPE:
   - Game Update/Patch
   - Bug Fix Confirmation
   - Policy Clarification
   - Community Response
   - Technical Issue Response

2. MARKET IMPACT ANALYSIS:
   - Which items/activities are affected?
   - Price impact prediction (High/Medium/Low)
   - Timeline of impact (Immediate/Short-term/Long-term)

3. GAME KNOWLEDGE EXTRACTION:
   - New mechanics revealed
   - Confirmed drop rates or statistics
   - Official clarifications on game systems

4. INTELLIGENCE VALUE:
   - How important is this information?
   - Should this trigger alerts to traders?
   - What follow-up monitoring is needed?

Use your complete OSRS wiki knowledge to provide detailed analysis of the implications.`;
      } else {
        analysisPrompt = `
Analyze this ${platform} post for OSRS market intelligence:

Author: ${author}
Content: ${content}
Platform: ${platform}

Determine:
1. CONTENT CLASSIFICATION:
   - Discussion topic (PvM, PvP, Skilling, Economy, etc.)
   - Items/activities mentioned
   - Community sentiment

2. MARKET RELEVANCE:
   - Does this discuss item prices or market trends?
   - Are there supply/demand implications?
   - Market impact potential (High/Medium/Low/None)

3. INFORMATION VALUE:
   - New strategies or methods discussed?
   - Community reactions to updates?
   - Useful data points for market analysis?

4. FOLLOW-UP ACTIONS:
   - Should we monitor this thread for mod responses?
   - Are there related items to track?
   - Is this part of a larger trend?

Use your OSRS knowledge to provide structured analysis.`;
      }

      // Call DeepSeek service for analysis
      const axios = require('axios');
      const response = await axios.post('http://localhost:5001/chat', {
        query: analysisPrompt
      });

      res.json({
        analysis: response.data.response,
        processing_time: response.data.processing_time,
        context_used: response.data.context_used,
        platform,
        author,
        is_official_source: isOfficialSource,
        analyzed_at: new Date().toISOString(),
        intelligence_priority: isOfficialSource ? 'HIGH' : 'MEDIUM'
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  detectOfficialSource(author, content, platform) {
    // Known Jagex mod usernames and patterns
    const officialPatterns = [
      // Reddit mod flairs and usernames
      /jagex.*mod/i,
      /mod.*jagex/i,
      /^jagex/i,
      /jmod/i,

      // Known mod usernames (add more as discovered)
      /mod_kieren/i,
      /mod_ash/i,
      /mod_ayiza/i,
      /mod_bruno/i,
      /mod_curse/i,
      /mod_elena/i,
      /mod_goblin/i,
      /mod_husky/i,
      /mod_light/i,
      /mod_oasis/i,
      /mod_roq/i,
      /mod_sarnie/i,
      /mod_tide/i,
      /mod_west/i,
      /mod_zuko/i,

      // Official account patterns
      /oldschoolrs/i,
      /runescape/i
    ];

    // Check author name
    const isOfficialAuthor = officialPatterns.some(pattern => pattern.test(author));

    // Check content for official language patterns
    const officialContentPatterns = [
      /we're looking into/i,
      /we've identified/i,
      /this will be fixed/i,
      /in the next update/i,
      /we're aware of/i,
      /hotfix/i,
      /patch notes/i
    ];

    const hasOfficialLanguage = officialContentPatterns.some(pattern => pattern.test(content));

    return isOfficialAuthor || hasOfficialLanguage;
  }

  async getEnhancedRedditPosts(req, res) {
    try {
      const limit = parseInt(req.query.limit) || 20;
      const redditDataPath = path.join(__dirname, '../reddit-collection-system/data/reddit/posts');
      const files = await fs.readdir(redditDataPath);
      const jsonFiles = files.filter(f => f.endsWith('.json')).slice(0, limit);

      const posts = [];
      for (const file of jsonFiles) {
        try {
          const content = await fs.readFile(path.join(redditDataPath, file), 'utf8');
          const fileData = JSON.parse(content);

          // Handle the data structure where posts are in a 'data' array
          const postsArray = fileData.data || [fileData];

          for (const post of postsArray) {
            const postContent = post.content || post.selftext || '';
            const fullText = post.title + ' ' + postContent;

            // Enhanced post data with AI analysis indicators
            const enhancedPost = {
              id: post.id,
              title: post.title,
              author: post.author,
              score: post.upvotes || post.score || 0,
              created: post.created_utc,
              url: post.url,
              selftext: postContent.substring(0, 300) + '...',
              subreddit: post.subreddit,
              num_comments: post.comments || post.num_comments || 0,
              flair: post.flair,
              is_official_source: this.detectOfficialSource(post.author, fullText, 'reddit'),
              has_ai_analysis: post.ai_analysis ? true : false,
              financial_relevance: post.ai_analysis?.analysis?.financial_relevance?.is_relevant || false,
              market_impact: post.ai_analysis?.analysis?.financial_relevance?.impact_level || 'unknown',
              osrs_items_mentioned: post.ai_analysis?.analysis?.osrs_context?.items_mentioned || [],
              sentiment: post.ai_analysis?.analysis?.sentiment?.overall || 'neutral'
            };

            posts.push(enhancedPost);
          }
        } catch (e) {
          console.error(`Error reading ${file}:`, e.message);
        }
      }

      // Sort by priority: official sources first, then by score
      posts.sort((a, b) => {
        if (a.is_official_source && !b.is_official_source) return -1;
        if (!a.is_official_source && b.is_official_source) return 1;
        return b.score - a.score;
      });

      res.json({
        posts,
        total: posts.length,
        official_sources: posts.filter(p => p.is_official_source).length,
        ai_analyzed: posts.filter(p => p.has_ai_analysis).length,
        financially_relevant: posts.filter(p => p.financial_relevance).length
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async analyzePostEnhanced(req, res) {
    try {
      const { content, platform, author, metadata } = req.body;
      const startTime = Date.now();

      console.log('🧠 Starting enhanced AI analysis for post...');

      // Detect if this is from a Jagex mod or official source
      const isOfficialSource = this.detectOfficialSource(author, content, platform);

      // Call DeepSeek service for enhanced analysis with OSRS semantic comparison
      const axios = require('axios');

      try {
        const deepseekResponse = await axios.post('http://localhost:5001/analyze-post-enhanced', {
          content: content,
          author: author,
          platform: platform,
          metadata: metadata,
          is_official_source: isOfficialSource
        }, {
          timeout: 0, // No timeout - let AI analysis complete properly
          headers: { 'Content-Type': 'application/json' }
        });

        const analysisResult = deepseekResponse.data;
        const processingTime = Date.now() - startTime;

        console.log(`✅ Enhanced analysis completed in ${processingTime}ms`);

        res.json({
          success: true,
          analysis: {
            summary: analysisResult.summary || 'Analysis completed',
            osrs_connections: analysisResult.osrs_connections || [],
            market_impact: analysisResult.market_impact || null,
            is_official_source: isOfficialSource,
            confidence: analysisResult.confidence || 0.8,
            sentiment: analysisResult.sentiment || 'neutral'
          },
          processing_time: processingTime,
          timestamp: new Date().toISOString()
        });

      } catch (deepseekError) {
        console.error('DeepSeek service error:', deepseekError.message);

        // Fallback to basic analysis if DeepSeek is unavailable
        const basicAnalysis = this.generateBasicAnalysis(content, author, isOfficialSource);
        const processingTime = Date.now() - startTime;

        res.json({
          success: true,
          analysis: basicAnalysis,
          processing_time: processingTime,
          timestamp: new Date().toISOString(),
          note: 'Using basic analysis - DeepSeek service unavailable'
        });
      }

    } catch (error) {
      console.error('Enhanced analysis error:', error);
      res.status(500).json({
        success: false,
        message: `Analysis failed: ${error.message}`,
        timestamp: new Date().toISOString()
      });
    }
  }

  generateBasicAnalysis(content, author, isOfficialSource) {
    // Basic keyword-based analysis as fallback
    const osrsKeywords = {
      'items': ['whip', 'dragon', 'rune', 'barrows', 'godsword', 'scimitar', 'bow', 'staff', 'armor', 'armour'],
      'activities': ['raid', 'boss', 'quest', 'skill', 'training', 'pvm', 'pvp', 'slayer'],
      'locations': ['varrock', 'falador', 'lumbridge', 'camelot', 'ardougne', 'yanille', 'seers'],
      'economy': ['gp', 'gold', 'price', 'market', 'trade', 'flip', 'profit', 'loss']
    };

    const connections = [];
    const contentLower = content.toLowerCase();

    for (const [category, keywords] of Object.entries(osrsKeywords)) {
      for (const keyword of keywords) {
        if (contentLower.includes(keyword)) {
          connections.push({
            item: keyword,
            description: `Mentioned in context of ${category}`
          });
        }
      }
    }

    return {
      summary: isOfficialSource ?
        'Official communication from Jagex staff detected' :
        'Community discussion about OSRS content',
      osrs_connections: connections.slice(0, 5), // Limit to top 5
      market_impact: connections.some(c => c.item.includes('price') || c.item.includes('market')) ?
        'Potential market relevance detected' : null,
      is_official_source: isOfficialSource,
      confidence: 0.6
    };
  }

  async batchAnalyzePosts(req, res) {
    try {
      const { post_ids, force_reanalyze = false } = req.body;
      const redditDataPath = path.join(__dirname, '../reddit-collection-system/data/reddit/posts');

      const results = [];

      for (const postId of post_ids) {
        try {
          const filePath = path.join(redditDataPath, `${postId}.json`);
          const content = await fs.readFile(filePath, 'utf8');
          const post = JSON.parse(content);

          // Skip if already analyzed and not forcing reanalysis
          if (post.ai_analysis && !force_reanalyze) {
            results.push({
              post_id: postId,
              status: 'already_analyzed',
              analysis: post.ai_analysis
            });
            continue;
          }

          // Analyze the post
          const analysisResult = await this.analyzePostContent(post);

          // Save updated post with analysis
          post.ai_analysis = analysisResult;
          await fs.writeFile(filePath, JSON.stringify(post, null, 2));

          results.push({
            post_id: postId,
            status: 'analyzed',
            analysis: analysisResult
          });

        } catch (error) {
          results.push({
            post_id: postId,
            status: 'error',
            error: error.message
          });
        }
      }

      res.json({
        results,
        total_processed: results.length,
        successful: results.filter(r => r.status === 'analyzed').length,
        errors: results.filter(r => r.status === 'error').length
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async analyzePostContent(post) {
    const content = post.title + '\n\n' + (post.selftext || '');
    const isOfficial = this.detectOfficialSource(post.author, content, 'reddit');

    // Create analysis prompt based on content type
    const analysisPrompt = isOfficial ?
      this.createOfficialSourcePrompt(post.author, content, 'reddit') :
      this.createCommunityPostPrompt(post.author, content, 'reddit');

    try {
      const axios = require('axios');
      const response = await axios.post('http://localhost:5001/chat', {
        query: analysisPrompt
      });

      return {
        processed: true,
        timestamp: new Date().toISOString(),
        is_official_source: isOfficial,
        ai_response: response.data.response,
        processing_time: response.data.processing_time,
        context_used: response.data.context_used,
        intelligence_priority: isOfficial ? 'HIGH' : 'MEDIUM'
      };
    } catch (error) {
      throw new Error(`AI analysis failed: ${error.message}`);
    }
  }

  createOfficialSourcePrompt(author, content, platform) {
    return `
🔥 OFFICIAL JAGEX SOURCE DETECTED! 🔥

Author: ${author}
Platform: ${platform}
Content: ${content}

This is from an official Jagex source. Provide detailed analysis:

1. ANNOUNCEMENT TYPE & IMPACT:
   - What type of official communication is this?
   - Which game systems/items are affected?
   - Immediate vs long-term market implications

2. MARKET INTELLIGENCE:
   - Price impact predictions for affected items
   - Supply/demand changes expected
   - Trading opportunities or risks

3. GAME KNOWLEDGE EXTRACTION:
   - New mechanics or systems revealed
   - Official statistics or drop rates
   - Policy changes or clarifications

4. STRATEGIC IMPLICATIONS:
   - How should traders respond?
   - What follow-up monitoring is needed?
   - Related items to watch

Use your complete OSRS wiki knowledge for detailed analysis.`;
  }

  createCommunityPostPrompt(author, content, platform) {
    return `
Analyze this OSRS community post for market intelligence:

Author: ${author}
Platform: ${platform}
Content: ${content}

Provide structured analysis:

1. CONTENT CLASSIFICATION:
   - Main topic (PvM, PvP, Skilling, Economy, etc.)
   - Items/activities discussed
   - Community sentiment

2. MARKET RELEVANCE:
   - Financial/trading implications
   - Supply/demand factors mentioned
   - Price trend indicators

3. INTELLIGENCE VALUE:
   - New strategies or methods
   - Community reactions to updates
   - Useful market data points

4. MONITORING RECOMMENDATIONS:
   - Should we track this thread for mod responses?
   - Related items to monitor
   - Part of larger trend?

Use OSRS knowledge for context and implications.`;
  }

  async getSocialStats(req, res) {
    try {
      const stats = {
        reddit: {
          total_posts: 0,
          recent_posts: 0,
          top_subreddits: []
        },
        discord: {
          total_messages: 0,
          recent_messages: 0,
          active_channels: []
        },
        twitter: {
          total_posts: 0,
          recent_posts: 0,
          trending_topics: []
        }
      };

      // Get Reddit stats
      try {
        const redditDataPath = path.join(__dirname, '../reddit-collection-system/data/reddit/posts');
        const files = await fs.readdir(redditDataPath);
        stats.reddit.total_posts = files.filter(f => f.endsWith('.json')).length;

        const recent = files.filter(f => {
          const stat = require('fs').statSync(path.join(redditDataPath, f));
          return Date.now() - stat.mtime.getTime() < 24 * 60 * 60 * 1000; // 24 hours
        });
        stats.reddit.recent_posts = recent.length;
      } catch (e) {
        console.error('Error getting Reddit stats:', e.message);
      }

      res.json(stats);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  start() {
    this.app.listen(this.port, '0.0.0.0', () => {
      console.log(`🚀 OSRS Control Panel API running on http://0.0.0.0:${this.port}`);
      console.log(`📱 Mobile-friendly dashboard available from any device on the network`);
      console.log(`📊 Dashboard available at http://0.0.0.0:${this.port}`);
    });
  }
}

// Start the server
if (require.main === module) {
  const api = new OSRSControlPanelAPI();
  api.start();
}

module.exports = OSRSControlPanelAPI;
